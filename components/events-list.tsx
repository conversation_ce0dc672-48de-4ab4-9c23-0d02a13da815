'use client'

import { useRealtime } from '@/lib/realtime-provider'
import { EventCard } from '@/components/event-card'

export function EventsList() {
  const { events } = useRealtime()

  if (events.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground text-lg">
          Пока нет событий
        </p>
        <p className="text-muted-foreground mt-2">
          Следите за обновлениями!
        </p>
      </div>
    )
  }

  // Разделяем события на предстоящие и прошедшие
  const now = new Date()
  const upcomingEvents = events.filter(event => new Date(event.event_date) >= now)
  const pastEvents = events.filter(event => new Date(event.event_date) < now)

  return (
    <div className="space-y-12">
      {/* Предстоящие события */}
      {upcomingEvents.length > 0 && (
        <section>
          <h3 className="text-2xl font-bold text-foreground mb-6">
            Предстоящие события
          </h3>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-1 lg:max-w-4xl">
            {upcomingEvents.map((event) => (
              <EventCard key={event.id} event={event} />
            ))}
          </div>
        </section>
      )}

      {/* Прошедшие события */}
      {pastEvents.length > 0 && (
        <section>
          <h3 className="text-2xl font-bold text-foreground mb-6">
            Прошедшие события
          </h3>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-1 lg:max-w-4xl">
            {pastEvents.map((event) => (
              <EventCard key={event.id} event={event} isPast />
            ))}
          </div>
        </section>
      )}
    </div>
  )
} 
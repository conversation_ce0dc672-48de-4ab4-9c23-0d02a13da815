-- 16 om on Air - Supabase Setup Script
-- Скопируйте и выполните этот код в Supabase Dashboard → SQL Editor

-- 1. Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 2. Create events table
CREATE TABLE IF NOT EXISTS events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    event_date TIMESTAMPTZ NOT NULL,
    city TEXT NOT NULL,
    venue TEXT NOT NULL,
    description TEXT,
    poster_url TEXT,
    ticket_url TEXT,
    lineup TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_events_event_date ON events(event_date);
CREATE INDEX IF NOT EXISTS idx_events_city ON events(city);

-- 4. Enable Row Level Security
ALTER TABLE events ENABLE ROW LEVEL SECURITY;

-- 5. Drop existing policies if they exist
DROP POLICY IF EXISTS "Public read access" ON events;
DROP POLICY IF EXISTS "Authenticated users can insert" ON events;
DROP POLICY IF EXISTS "Authenticated users can update" ON events;
DROP POLICY IF EXISTS "Authenticated users can delete" ON events;

-- 6. Create RLS policies
CREATE POLICY "Public read access" ON events
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can insert" ON events
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update" ON events
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete" ON events
    FOR DELETE USING (auth.role() = 'authenticated');

-- 7. Create storage bucket (выполните отдельно, если возникнут ошибки)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'posters',
    'posters',
    true,
    5242880,
    ARRAY['image/jpeg', 'image/png', 'image/webp']
) ON CONFLICT (id) DO NOTHING;

-- 8. Drop existing storage policies if they exist
DROP POLICY IF EXISTS "Public read access to posters" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload posters" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update posters" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete posters" ON storage.objects;

-- 9. Create storage policies
CREATE POLICY "Public read access to posters" ON storage.objects
    FOR SELECT USING (bucket_id = 'posters');

CREATE POLICY "Authenticated users can upload posters" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'posters' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update posters" ON storage.objects
    FOR UPDATE USING (bucket_id = 'posters' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete posters" ON storage.objects
    FOR DELETE USING (bucket_id = 'posters' AND auth.role() = 'authenticated');

-- 10. Test the setup
SELECT 'Setup completed successfully!' as status; 
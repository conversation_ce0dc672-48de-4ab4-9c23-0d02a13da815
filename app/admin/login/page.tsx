'use client'

import { useState, useEffect, Suspense } from 'react'
import { signInWithPassword, signUp, resetPassword, updatePassword, getErrorMessage, handleAuthCallback, checkSession } from '@/lib/auth'
import { useSearchParams, useRouter } from 'next/navigation'
import Link from 'next/link'

type AuthMode = 'login' | 'register' | 'reset' | 'update-password'

function AdminLoginForm() {
  const [mode, setMode] = useState<AuthMode>('login')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)
  const searchParams = useSearchParams()
  const router = useRouter()

  useEffect(() => {
    const checkAuthCallback = async () => {
      try {
        // Сначала проверяем, есть ли уже активная сессия
        const existingSession = await checkSession()
        if (existingSession) {
          console.log('Найдена существующая сессия, перенаправляем в админ-панель')
          router.push('/admin/dashboard')
          return
        }

        // Затем проверяем callback токены в URL hash
        const result = await handleAuthCallback()
        if (result) {
          if (result.type === 'recovery') {
            setMode('update-password')
            setMessage('Введите новый пароль')
          } else {
            // Обычный вход - перенаправляем в админ-панель
            router.push('/admin/dashboard')
            return
          }
        }
      } catch (error) {
        console.error('Auth callback error:', error)
        setError(getErrorMessage(error as Error))
      } finally {
        setIsCheckingAuth(false)
      }
    }

    checkAuthCallback()
  }, [router])

  useEffect(() => {
    if (isCheckingAuth) return // Не обрабатываем URL параметры пока проверяем auth callback
    
    const errorParam = searchParams.get('error')
    const typeParam = searchParams.get('type')
    
    if (typeParam === 'recovery' && mode !== 'update-password') {
      // Если type=recovery в URL, но токенов нет в hash, показываем ошибку
      setError('Ссылка для сброса пароля недействительна или истекла')
    } else if (errorParam === 'auth_failed') {
      setError('Ошибка при входе. Попробуйте еще раз.')
    } else if (errorParam === 'no_session') {
      setError('Не удалось установить сессию. Попробуйте еще раз.')
    } else if (errorParam === 'callback_error') {
      setError('Ошибка при обработке входа. Попробуйте еще раз.')
    } else if (errorParam === 'no_auth_data') {
      setError('Не удалось получить данные аутентификации. Попробуйте еще раз.')
    }
  }, [searchParams, isCheckingAuth, mode])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setMessage('')

    try {
      if (mode === 'login') {
        console.log('Попытка входа с:', email)
        const result = await signInWithPassword(email, password)
        console.log('Результат входа:', { user: result.user?.email, session: !!result.session })
        
        // Небольшая задержка для того, чтобы сессия успела установиться
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Проверяем, что сессия действительно установлена
        const session = await checkSession()
        if (!session) {
          throw new Error('Сессия не была установлена после входа')
        }
        
        console.log('Сессия подтверждена, перенаправляем в админ-панель')
        router.push('/admin/dashboard')
      } else if (mode === 'register') {
        if (password !== confirmPassword) {
          setError('Пароли не совпадают')
          return
        }
        await signUp(email, password)
        setMessage('Регистрация успешна! Проверьте почту для подтверждения email.')
        setMode('login')
      } else if (mode === 'reset') {
        await resetPassword(email)
        setMessage('Ссылка для сброса пароля отправлена на вашу почту!')
      } else if (mode === 'update-password') {
        if (password !== confirmPassword) {
          setError('Пароли не совпадают')
          return
        }
        await updatePassword(password)
        setMessage('Пароль успешно обновлен!')
        setMode('login')
      }
    } catch (err) {
      console.error('Auth error:', err)
      setError(getErrorMessage(err as Error))
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setEmail('')
    setPassword('')
    setConfirmPassword('')
    setError('')
    setMessage('')
  }

  const switchMode = (newMode: AuthMode) => {
    setMode(newMode)
    resetForm()
  }

  const getTitle = () => {
    switch (mode) {
      case 'login': return 'Вход в админ-панель'
      case 'register': return 'Регистрация'
      case 'reset': return 'Сброс пароля'
      case 'update-password': return 'Новый пароль'
      default: return 'Вход в админ-панель'
    }
  }

  const getButtonText = () => {
    if (loading) return 'Загрузка...'
    switch (mode) {
      case 'login': return 'Войти'
      case 'register': return 'Зарегистрироваться'
      case 'reset': return 'Отправить ссылку'
      case 'update-password': return 'Обновить пароль'
      default: return 'Войти'
    }
  }

  // Показываем загрузку пока проверяем auth callback
  return isCheckingAuth ? (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Проверка аутентификации...</p>
      </div>
    </div>
  ) : (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <h1 className="brand-logo text-2xl mb-2">16 om on Air</h1>
          <h2 className="text-xl font-semibold text-foreground">
            {getTitle()}
          </h2>
          <p className="text-muted-foreground mt-2">
            {mode === 'register' ? 'Создание аккаунта для управления событиями' : 'Управление событиями'}
          </p>
        </div>

        {mode !== 'update-password' && (
          <div className="flex space-x-1 bg-muted p-1 rounded-md">
            <button
              type="button"
              onClick={() => switchMode('login')}
              className={`flex-1 py-2 px-3 rounded text-sm font-medium transition-colors ${
                mode === 'login' 
                  ? 'bg-background text-foreground shadow-sm' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              Вход
            </button>
            <button
              type="button"
              onClick={() => switchMode('register')}
              className={`flex-1 py-2 px-3 rounded text-sm font-medium transition-colors ${
                mode === 'register' 
                  ? 'bg-background text-foreground shadow-sm' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              Регистрация
            </button>
            <button
              type="button"
              onClick={() => switchMode('reset')}
              className={`flex-1 py-2 px-3 rounded text-sm font-medium transition-colors ${
                mode === 'reset' 
                  ? 'bg-background text-foreground shadow-sm' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              Сброс
            </button>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-foreground mb-2">
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={mode === 'update-password'}
              className="w-full px-4 py-2 bg-input border border-border rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary disabled:opacity-50"
              placeholder="<EMAIL>"
            />
          </div>

          {mode !== 'reset' && (
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-foreground mb-2">
                {mode === 'update-password' ? 'Новый пароль' : 'Пароль'}
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                className="w-full px-4 py-2 bg-input border border-border rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Минимум 6 символов"
                minLength={6}
              />
            </div>
          )}

          {(mode === 'register' || mode === 'update-password') && (
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-foreground mb-2">
                Подтвердите пароль
              </label>
              <input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                className="w-full px-4 py-2 bg-input border border-border rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Повторите пароль"
                minLength={6}
              />
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {getButtonText()}
          </button>

          {message && (
            <div className="p-4 bg-primary/10 border border-primary/20 rounded-md">
              <p className="text-sm text-primary">{message}</p>
            </div>
          )}

          {error && (
            <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-md">
              <p className="text-sm text-destructive">{error}</p>
            </div>
          )}
        </form>

        <div className="text-center">
          <Link
            href="/"
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            ← Назад к афише
          </Link>
        </div>
      </div>
    </div>
  )
}

export default function AdminLoginPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Загрузка...</p>
        </div>
      </div>
    }>
      <AdminLoginForm />
    </Suspense>
  )
} 
import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !serviceRoleKey) {
  console.error('Missing Supabase credentials in .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Реальные предстоящие события российской техно-сцены
const sampleEvents = [
  {
    title: "Outline Festival 2025",
    event_date: "2025-07-10T18:00:00+03:00",
    city: "Дубна",
    venue: "Outline Festival Grounds",
    description: "Главный летний фестиваль электронной музыки в России. В лайнапе: <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> <PERSON>rejcha и многие другие.",
    poster_url: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=600&fit=crop&auto=format",
    ticket_url: "https://outlinefestival.org/tickets",
    lineup: ["Amelie Lens", "Charlotte de Witte", "Nina Kraviz", "Boris Brejcha", "I Hate Models", "Kobosil"]
  },
  {
    title: "Базовое Техно: Blank",
    event_date: "2025-06-06T23:00:00+03:00",
    city: "Москва",
    venue: "Blank",
    description: "Вечеринка от российского техно-лейбла Базовое Техно. Резиденты лейбла представят лучшие треки из каталога.",
    poster_url: "https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?w=800&h=600&fit=crop&auto=format",
    ticket_url: "https://bazovoetechno.online/tickets",
    lineup: ["Keyser", "Maks Freezbee", "Zugzwang", "Elena Fomina"]
  },
  {
    title: "Sputnik Springbreak 2025",
    event_date: "2025-06-06T17:00:00+03:00",
    city: "Биттерфельд",
    venue: "Goitzschesee",
    description: "Крупнейший техно-фестиваль в Германии с участием российских артистов. В программе: I Hate Models, Kobosil, Lilly Palmer.",
    poster_url: "https://images.unsplash.com/photo-1571266028243-d220c6c0b8ee?w=800&h=600&fit=crop&auto=format",
    ticket_url: "https://sputnikspringbreak.com/tickets",
    lineup: ["I Hate Models", "Kobosil", "Lilly Palmer", "Angerfist", "Scooter"]
  },
  {
    title: "DZHURA Symbiont Universe",
    event_date: "2025-11-09T22:00:00+03:00",
    city: "Санкт-Петербург",
    venue: "Angar",
    description: "Презентация нового альбома от DZHURA. Техно-сет от одного из ведущих российских продюсеров.",
    poster_url: "https://images.unsplash.com/photo-1540039155733-5bb30b53aa14?w=800&h=600&fit=crop&auto=format",
    ticket_url: "https://spb.qtickets.events/133658-symbiont-black-records-showcase-09112024",
    lineup: ["DZHURA", "Symbiont Artists"]
  },
  {
    title: "Nina Kraviz at Gipsy",
    event_date: "2025-08-15T23:00:00+03:00",
    city: "Москва", 
    venue: "Gipsy",
    description: "Эксклюзивный сет от Нины Кравиц в легендарном московском клубе. Ограниченное количество билетов.",
    poster_url: "https://images.unsplash.com/photo-1470229722913-7c0e2dbbafd3?w=800&h=600&fit=crop&auto=format",
    ticket_url: "https://gipsy.ru/tickets/nina-kraviz",
    lineup: ["Nina Kraviz"]
  },
  {
    title: "Korolova Live",
    event_date: "2025-07-19T21:00:00+03:00",
    city: "Москва",
    venue: "Aglomerat",
    description: "Живое выступление Korolova - одной из самых ярких звезд российской техно-сцены.",
    poster_url: "https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=800&h=600&fit=crop&auto=format",
    ticket_url: "https://aglomerat.club/tickets",
    lineup: ["Korolova", "Local Support"]
  },
  {
    title: "PPK Reunion Show",
    event_date: "2025-09-20T20:00:00+03:00",
    city: "Ростов-на-Дону",
    venue: "Podval Club",
    description: "Воссоединение легендарного дуэта PPK. Исполнение классических треков и новый материал.",
    poster_url: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&auto=format",
    ticket_url: "https://podval-club.ru/tickets/ppk",
    lineup: ["PPK", "Guest DJs"]
  },
  {
    title: "AIGEL Electronic Night",
    event_date: "2025-04-23T20:00:00+03:00", 
    city: "Казань",
    venue: "Kazan Expo",
    description: "Электронный концерт дуэта AIGEL. Смесь электроники, инди-попа и экспериментального звука.",
    poster_url: "https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=800&h=600&fit=crop&auto=format",
    ticket_url: "https://kazan-expo.ru/tickets",
    lineup: ["AIGEL", "Supporting Acts"]
  }
]

// Прошедшие события
const pastEvents = [
  {
    title: "Awakenings x ADE 2024",
    event_date: "2024-10-19T22:00:00+02:00",
    city: "Амстердам",
    venue: "Gashouder",
    description: "Участие российских артистов в главном техно-событии Европы. Nina Kraviz закрывала главную сцену.",
    poster_url: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=600&fit=crop&auto=format",
    ticket_url: "https://awakenings.nl/tickets",
    lineup: ["Nina Kraviz", "Charlotte de Witte", "Amelie Lens", "Adam Beyer"]
  },
  {
    title: "Outline Festival 2024",
    event_date: "2024-07-11T18:00:00+03:00",
    city: "Дубна", 
    venue: "Outline Festival Grounds",
    description: "Прошлогодний Outline с участием мировых звезд техно. Более 50 000 посетителей за 4 дня.",
    poster_url: "https://images.unsplash.com/photo-1516450360452-9312f5e86fc7?w=800&h=600&fit=crop&auto=format",
    ticket_url: "https://outlinefestival.org/archive/2024",
    lineup: ["Carl Cox", "Charlotte de Witte", "Boris Brejcha", "Maceo Plex"]
  },
  {
    title: "Electroforez Winter Session",
    event_date: "2024-12-31T23:00:00+03:00",
    city: "Москва",
    venue: "Aglomerat",
    description: "Новогодняя техно-вечеринка от коллектива Electroforez. 12 часов непрерывной музыки.",
    poster_url: "https://images.unsplash.com/photo-1467810563316-b5476525c0f9?w=800&h=600&fit=crop&auto=format",
    ticket_url: "https://electroforez.com/tickets",
    lineup: ["Electroforez Collective", "Guest Artists"]
  }
]

async function setupDatabase() {
  try {
    console.log('🚀 Setting up Supabase database...')

    // 1. Check if table exists
    console.log('📝 Checking database connection...')
    const { error: checkError } = await supabase
      .from('events')
      .select('id')
      .limit(1)

    if (checkError && checkError.code === '42P01') {
      console.error('❌ Database table does not exist. Please run the SQL manually in Supabase Dashboard.')
      console.log(`
📋 Please execute this SQL in your Supabase Dashboard (SQL Editor):

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create events table
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    event_date TIMESTAMPTZ NOT NULL,
    city TEXT NOT NULL,
    venue TEXT NOT NULL,
    description TEXT,
    poster_url TEXT,
    ticket_url TEXT,
    lineup TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Set up Row Level Security (RLS)
ALTER TABLE events ENABLE ROW LEVEL SECURITY;

-- Allow public read access to events
CREATE POLICY "Public read access" ON events
    FOR SELECT USING (true);

-- Allow authenticated users to insert, update, and delete events
CREATE POLICY "Authenticated users can insert" ON events
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update" ON events
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete" ON events
    FOR DELETE USING (auth.role() = 'authenticated');

-- Create index for better performance
CREATE INDEX idx_events_event_date ON events(event_date);
CREATE INDEX idx_events_city ON events(city);
      `)
      return
    }

    // 2. Clear existing events
    console.log('🧹 Clearing existing events...')
    await supabase.from('events').delete().neq('id', '00000000-0000-0000-0000-000000000000')

    // 3. Insert sample events
    console.log('🎵 Adding upcoming events...')
    const { error: eventsError } = await supabase
      .from('events')
      .insert(sampleEvents)

    if (eventsError) {
      console.error('❌ Error inserting upcoming events:', eventsError)
    } else {
      console.log(`✅ Added ${sampleEvents.length} upcoming events`)
    }

    // 4. Insert past events
    console.log('📅 Adding past events...')
    const { error: pastError } = await supabase
      .from('events')
      .insert(pastEvents)

    if (pastError) {
      console.error('❌ Error inserting past events:', pastError)
    } else {
      console.log(`✅ Added ${pastEvents.length} past events`)
    }

    // 5. Verify data
    const { data: allEvents, error: verifyError } = await supabase
      .from('events')
      .select('*')
      .order('event_date', { ascending: false })

    if (verifyError) {
      console.error('❌ Error verifying events:', verifyError)
    } else {
      console.log(`✅ Database setup complete! Total events: ${allEvents.length}`)
      console.log('📊 Events by city:')
      const cityCounts = allEvents.reduce((acc, event) => {
        acc[event.city] = (acc[event.city] || 0) + 1
        return acc
      }, {})
      Object.entries(cityCounts).forEach(([city, count]) => {
        console.log(`   ${city}: ${count} events`)
      })
    }

    console.log('\n🎉 Setup completed successfully!')
    console.log('🌐 Your app is now ready with real event data!')
    console.log('🔗 Visit http://localhost:3000 to see the result')

  } catch (error) {
    console.error('❌ Setup failed:', error)
    process.exit(1)
  }
}

setupDatabase() 
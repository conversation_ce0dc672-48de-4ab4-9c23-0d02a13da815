import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Загружаем переменные окружения
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Supabase URL и service role ключ должны быть установлены в .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function clearEvents() {
  console.log('🗑️  Начинаю очистку базы данных событий...')
  
  try {
    // Сначала получаем количество событий
    const { count, error: countError } = await supabase
      .from('events')
      .select('*', { count: 'exact', head: true })
    
    if (countError) {
      console.error('❌ Ошибка при подсчете событий:', countError)
      return
    }
    
    console.log(`📊 Найдено событий в базе: ${count}`)
    
    if (count === 0) {
      console.log('✅ База данных уже пуста!')
      return
    }
    
    // Удаляем все события
    const { error } = await supabase
      .from('events')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000') // Удаляем все записи
    
    if (error) {
      console.error('❌ Ошибка при очистке базы данных:', error)
      return
    }
    
    console.log(`✅ Успешно удалено ${count} событий из базы данных`)
    console.log('🎉 Очистка базы данных завершена!')
    
  } catch (error) {
    console.error('❌ Критическая ошибка:', error)
  }
}

// Запускаем скрипт
clearEvents() 
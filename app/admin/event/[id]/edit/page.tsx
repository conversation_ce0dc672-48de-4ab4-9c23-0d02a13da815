'use client'

import { useState, useEffect } from 'react'
import { getEventById, updateEvent, uploadPoster } from '@/lib/events'
import { getCurrentUser } from '@/lib/auth'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Event } from '@/lib/supabase/client'

interface EditEventPageProps {
  params: Promise<{
    id: string
  }>
}

export default function EditEventPage({ params }: EditEventPageProps) {
  return <EditEventPageClient params={params} />
}

function EditEventPageClient({ params }: { params: Promise<{ id: string }> }) {
  const [mounted, setMounted] = useState(false)
  const [user, setUser] = useState<{ email: string } | null>(null)
  const [event, setEvent] = useState<Event | null>(null)
  const [loading, setLoading] = useState(false)
  const [eventId, setEventId] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    title: '',
    event_date: '',
    city: '',
    venue: '',
    description: '',
    ticket_url: '',
    lineup: '',
  })
  const [posterFile, setPosterFile] = useState<File | null>(null)
  const [error, setError] = useState('')
  const router = useRouter()

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    const initializeParams = async () => {
      const resolvedParams = await params
      setEventId(resolvedParams.id)
    }
    initializeParams()
  }, [params])

  useEffect(() => {
    if (eventId) {
      checkAuth()
      loadEvent()
    }
  }, [eventId]) // eslint-disable-line react-hooks/exhaustive-deps

  const checkAuth = async () => {
    try {
      const currentUser = await getCurrentUser()
      if (!currentUser) {
        router.push('/admin/login')
        return
      }
      setUser({ email: currentUser.email || '' })
    } catch (error) {
      console.error('Auth check error:', error)
      router.push('/admin/login')
    }
  }

  const loadEvent = async () => {
    if (!eventId) return
    
    try {
      const eventData = await getEventById(eventId)
      if (!eventData) {
        router.push('/admin/dashboard')
        return
      }
      
      setEvent(eventData)
      setFormData({
        title: eventData.title,
        event_date: eventData.event_date.slice(0, 16), // Format for datetime-local
        city: eventData.city,
        venue: eventData.venue,
        description: eventData.description || '',
        ticket_url: eventData.ticket_url || '',
        lineup: eventData.lineup?.join(', ') || '',
      })
    } catch (error) {
      console.error('Error loading event:', error)
      router.push('/admin/dashboard')
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setPosterFile(file)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!eventId) return
    
    setLoading(true)
    setError('')

    try {
      let posterUrl = event?.poster_url || ''
      
      if (posterFile) {
        posterUrl = await uploadPoster(posterFile)
      }

      const lineup = formData.lineup
        .split(',')
        .map(artist => artist.trim())
        .filter(artist => artist.length > 0)

      await updateEvent(eventId, {
        title: formData.title,
        event_date: formData.event_date,
        city: formData.city,
        venue: formData.venue,
        description: formData.description || null,
        poster_url: posterUrl || null,
        ticket_url: formData.ticket_url || null,
        lineup: lineup.length > 0 ? lineup : null,
      })

      router.push('/admin/dashboard')
    } catch (error) {
      console.error('Error updating event:', error)
      setError('Ошибка при обновлении события. Попробуйте еще раз.')
    } finally {
      setLoading(false)
    }
  }

  if (!mounted) {
    return null
  }

  return (!eventId || !user || !event) ? (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Загрузка...</p>
      </div>
    </div>
  ) : (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border admin-header">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/" className="brand-logo">
                16 om on Air
              </Link>
              <span className="text-muted-foreground">Админ-панель</span>
            </div>
            <Link
              href="/admin/dashboard"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              ← Назад к дашборду
            </Link>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 admin-container">
        <div className="max-w-2xl mx-auto event-form-container">
          <h1 className="text-3xl font-bold text-foreground mb-8">
            Редактировать событие
          </h1>

          <form onSubmit={handleSubmit} className="space-y-6 event-form">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-foreground mb-2">
                Название события *
              </label>
              <input
                id="title"
                name="title"
                type="text"
                value={formData.title}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-2 bg-input border border-border rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="16 om on Air: Techno Night"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="event_date" className="block text-sm font-medium text-foreground mb-2">
                  Дата и время *
                </label>
                <input
                  id="event_date"
                  name="event_date"
                  type="datetime-local"
                  value={formData.event_date}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 bg-input border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>

              <div>
                <label htmlFor="city" className="block text-sm font-medium text-foreground mb-2">
                  Город *
                </label>
                <input
                  id="city"
                  name="city"
                  type="text"
                  value={formData.city}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-2 bg-input border border-border rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Москва"
                />
              </div>
            </div>

            <div>
              <label htmlFor="venue" className="block text-sm font-medium text-foreground mb-2">
                Место проведения *
              </label>
              <input
                id="venue"
                name="venue"
                type="text"
                value={formData.venue}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-2 bg-input border border-border rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Клуб Arma17"
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-foreground mb-2">
                Описание
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-4 py-2 bg-input border border-border rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Описание события..."
              />
            </div>

            <div>
              <label htmlFor="lineup" className="block text-sm font-medium text-foreground mb-2">
                Lineup (через запятую)
              </label>
              <input
                id="lineup"
                name="lineup"
                type="text"
                value={formData.lineup}
                onChange={handleInputChange}
                className="w-full px-4 py-2 bg-input border border-border rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Natasha Wax, Sony Vibe, Guest Artist"
              />
            </div>

            <div>
              <label htmlFor="ticket_url" className="block text-sm font-medium text-foreground mb-2">
                Ссылка на билеты
              </label>
              <input
                id="ticket_url"
                name="ticket_url"
                type="url"
                value={formData.ticket_url}
                onChange={handleInputChange}
                className="w-full px-4 py-2 bg-input border border-border rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="https://tickets.example.com"
              />
            </div>

            <div>
              <label htmlFor="poster" className="block text-sm font-medium text-foreground mb-2">
                Постер
              </label>
              {event.poster_url && (
                <div className="mb-4">
                  <Image
                    src={event.poster_url}
                    alt="Текущий постер"
                    width={128}
                    height={128}
                    className="w-32 h-32 object-cover rounded-md"
                  />
                  <p className="text-sm text-muted-foreground mt-2">
                    Текущий постер (загрузите новый файл для замены)
                  </p>
                </div>
              )}
              <input
                id="poster"
                name="poster"
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                className="w-full px-4 py-2 bg-input border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-secondary"
              />
            </div>

            {error && (
              <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-md">
                <p className="text-sm text-destructive">{error}</p>
              </div>
            )}

            <div className="flex gap-4">
              <button
                type="submit"
                disabled={loading}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Сохранение...' : 'Сохранить изменения'}
              </button>
              <Link
                href="/admin/dashboard"
                className="btn-secondary"
              >
                Отмена
              </Link>
            </div>
          </form>
        </div>
      </main>
    </div>
  )
} 
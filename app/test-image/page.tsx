'use client'

import { useState } from 'react'

export default function TestImagePage() {
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [base64Result, setBase64Result] = useState<string | null>(null)

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedImage(file)
      
      // Создаем превью
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const convertToBase64 = async () => {
    if (!selectedImage) return

    try {
      const base64 = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => {
          const result = reader.result as string
          // Убираем префикс data:image/...;base64,
          const base64 = result.split(',')[1]
          resolve(base64)
        }
        reader.onerror = reject
        reader.readAsDataURL(selectedImage)
      })
      
      setBase64Result(base64)
      console.log('Base64 длина:', base64.length)
    } catch (error) {
      console.error('Ошибка при конвертации:', error)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Тест загрузки изображений</h1>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            Выберите изображение:
          </label>
          <input
            type="file"
            accept="image/*"
            onChange={handleImageSelect}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>

        {imagePreview && (
          <div>
            <h3 className="text-lg font-medium mb-2">Превью:</h3>
            <img
              src={imagePreview}
              alt="Превью"
              className="max-w-md max-h-64 object-contain border rounded"
            />
          </div>
        )}

        {selectedImage && (
          <div>
            <button
              onClick={convertToBase64}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Конвертировать в Base64
            </button>
          </div>
        )}

        {base64Result && (
          <div>
            <h3 className="text-lg font-medium mb-2">Base64 результат:</h3>
            <div className="bg-gray-100 p-4 rounded max-h-32 overflow-y-auto">
              <code className="text-xs break-all">{base64Result.substring(0, 200)}...</code>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Длина: {base64Result.length} символов
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

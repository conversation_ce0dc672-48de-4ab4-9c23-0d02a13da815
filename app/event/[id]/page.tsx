import { getEventById } from '@/lib/events'
import { notFound } from 'next/navigation'
import { format } from 'date-fns'
import { ru } from 'date-fns/locale'
import Link from 'next/link'
import Image from 'next/image'
import { Metadata } from 'next'

interface EventPageProps {
  params: Promise<{
    id: string
  }>
}

export async function generateMetadata({ params }: EventPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const event = await getEventById(resolvedParams.id)
  
  if (!event) {
    return {
      title: 'Событие не найдено',
    }
  }

  return {
    title: `${event.title} - 16 om on Air`,
    description: event.description || `${event.title} в ${event.city}, ${event.venue}`,
    openGraph: {
      title: `${event.title} - 16 om on Air`,
      description: event.description || `${event.title} в ${event.city}, ${event.venue}`,
      images: event.poster_url ? [
        {
          url: event.poster_url,
          width: 1200,
          height: 630,
          alt: event.title,
        },
      ] : [],
    },
  }
}

export default async function EventPage({ params }: EventPageProps) {
  const resolvedParams = await params
  const event = await getEventById(resolvedParams.id)

  if (!event) {
    notFound()
  }

  const eventDate = new Date(event.event_date)
  const fullDate = format(eventDate, 'dd MMMM yyyy', { locale: ru })
  const time = format(eventDate, 'HH:mm', { locale: ru })
  const weekday = format(eventDate, 'EEEE', { locale: ru })

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <Link href="/" className="brand-logo">
              16 om on Air
            </Link>
            <Link
              href="/"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              ← Назад к афише
            </Link>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="grid gap-8 md:grid-cols-2">
            {/* Постер */}
            {event.poster_url && (
              <div className="order-2 md:order-1">
                <Image
                  src={event.poster_url}
                  alt={event.title}
                  width={600}
                  height={800}
                  className="w-full h-auto rounded-lg shadow-lg"
                />
              </div>
            )}

            {/* Информация о событии */}
            <div className="order-1 md:order-2 space-y-6">
              <div>
                <h1 className="text-4xl font-bold text-foreground mb-4">
                  {event.title}
                </h1>
                <div className="space-y-2">
                  <p className="text-xl text-primary font-semibold">
                    {fullDate}
                  </p>
                  <p className="text-lg text-muted-foreground capitalize">
                    {weekday}, {time}
                  </p>
                  <p className="text-lg text-muted-foreground">
                    {event.city} • {event.venue}
                  </p>
                </div>
              </div>

              {event.description && (
                <div>
                  <h3 className="text-xl font-semibold mb-2">О событии</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {event.description}
                  </p>
                </div>
              )}

              {event.lineup && event.lineup.length > 0 && (
                <div>
                  <h3 className="text-xl font-semibold mb-3">Lineup</h3>
                  <div className="space-y-2">
                    {event.lineup.map((artist, index) => (
                      <div
                        key={index}
                        className="bg-muted px-4 py-2 rounded-lg"
                      >
                        {artist}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <span className="text-muted-foreground">Возрастное ограничение:</span>
                  <span className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full text-sm font-medium">
                    18+
                  </span>
                </div>

                {event.ticket_url && (
                  <a
                    href={event.ticket_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-primary inline-block text-center"
                  >
                    Купить билеты
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>

      <footer className="border-t border-border mt-16">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <p className="text-muted-foreground text-sm">
              © 2024 16 om on Air. Все права защищены.
            </p>
            <p className="text-muted-foreground text-sm">
              YouTube: 10 млн+ просмотров
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
} 
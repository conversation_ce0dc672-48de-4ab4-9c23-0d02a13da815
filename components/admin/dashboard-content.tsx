'use client'

import { useState, useEffect } from 'react'
import { getAllEvents, deleteEvent } from '@/lib/events'
import { signOut, getCurrentUser } from '@/lib/auth'
import { Event } from '@/lib/supabase/client'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { AdminEventCard } from '@/components/admin/admin-event-card'
import { AIChat } from '@/components/admin/ai-chat'

export function DashboardContent() {
  const [events, setEvents] = useState<Event[]>([])
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<{ email: string } | null>(null)
  const router = useRouter()

  useEffect(() => {
    const checkAuthAndLoadData = async () => {
      try {
        const currentUser = await getCurrentUser()
        if (!currentUser) {
          router.push('/admin/login')
          return
        }
        setUser({ email: currentUser.email || '' })
        
        const eventsData = await getAllEvents()
        setEvents(eventsData)
      } catch (error) {
        console.error('Error during setup:', error)
        // Optionally redirect or show an error message
      } finally {
        setLoading(false)
      }
    }

    checkAuthAndLoadData()
  }, [router])

  const handleSignOut = async () => {
    try {
      await signOut()
      router.push('/admin/login')
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  const handleDeleteEvent = async (id: string) => {
    if (!confirm('Вы уверены, что хотите удалить это событие?')) {
      return
    }

    try {
      await deleteEvent(id)
      setEvents(events.filter(event => event.id !== id))
    } catch (error) {
      console.error('Error deleting event:', error)
      alert('Ошибка при удалении события')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Загрузка...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <header className="admin-header">
        <div className="admin-header-inner">
          <div className="admin-header-left">
            <Link href="/" className="brand-logo">
              Natasha Wax & Sony Vibe
            </Link>
            <span className="admin-panel-label">Админ-панель</span>
          </div>
          <div className="admin-header-right">
            <span className="admin-user-email">{user?.email}</span>
            <button onClick={handleSignOut} className="admin-signout-btn">Выйти</button>
          </div>
        </div>
      </header>

      <main className="admin-container">
        <div className="admin-actions">
          <h1 className="admin-title">Управление событиями</h1>
          <Link href="/admin/event/new" className="btn-primary">Добавить событие</Link>
        </div>

        {/* AI Ассистент */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">AI Ассистент</h2>
          <AIChat />
        </div>

        {events.length === 0 ? (
          <div className="admin-empty">
            <p className="admin-empty-text">Нет созданных событий</p>
            <Link href="/admin/event/new" className="btn-primary">Создать первое событие</Link>
          </div>
        ) : (
          <div className="admin-event-list">
            {events.map((event) => (
              <AdminEventCard key={event.id} event={event} onDelete={handleDeleteEvent} />
            ))}
          </div>
        )}
      </main>
    </div>
  )
}

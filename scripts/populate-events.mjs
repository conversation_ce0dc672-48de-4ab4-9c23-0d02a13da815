import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'

// Загружаем переменные окружения
dotenv.config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Supabase URL и service role ключ должны быть установлены в .env.local')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

// Реальные данные событий
const eventsData = [
  {
    title: 'Sony Vibe Happy Birthday',
    event_date: '2025-05-16T21:00:00+03:00',
    city: 'Москва',
    venue: 'Noor Electro',
    description: 'День рождения Sony Vibe — особенная вечеринка с лучшими треками и незабываемой атмосферой электронной музыки.',
    poster_url: 'https://rupor.events/storage/16376/conversions/01JTNA59EB1SMF0E010J52P5RT-poster.webp',
    lineup: ['Sony Vibe', 'Special Guests']
  },
  {
    title: 'Natasha Wax & Sony Vibe',
    event_date: '2025-05-24T22:00:00+03:00',
    city: 'Нижний Новгород',
    venue: 'Escape Asia',
    description: 'Совместное выступление Natasha Wax и Sony Vibe в Нижнем Новгороде. Два талантливых артиста на одной сцене.',
    poster_url: 'https://rupor.events/storage/16854/conversions/01JVQQHDM9VNSMGSVE6B04R1H4-poster.webp',
    lineup: ['Natasha Wax', 'Sony Vibe']
  },
  {
    title: 'TRIP 2025 Official Pre-Party',
    event_date: '2025-06-11T23:00:00+03:00',
    city: 'Москва',
    venue: 'Blanc',
    description: 'Официальная пре-пати фестиваля TRIP 2025. Разогрев перед главным событием года в мире электронной музыки.',
    poster_url: 'https://rupor.events/storage/16819/conversions/01JVFV7NYR3R40R488E2FNGCGW-poster.webp',
    lineup: ['Sony Vibe', 'Natasha Wax', 'Special Acts']
  },
  {
    title: 'Freak Boutique',
    event_date: '2025-06-06T22:30:00+03:00',
    city: 'Москва',
    venue: 'Blanc',
    description: 'Эксклюзивная вечеринка Freak Boutique — место встречи ценителей качественной электронной музыки и стиля.',
    poster_url: 'https://rupor.events/storage/16821/conversions/01JVFWG1SNYJPPECXPX6DAB468-poster.webp',
    lineup: ['Sony Vibe', 'Natasha Wax', 'Resident DJs']
  },
  {
    title: '«Возрождение» фестиваль',
    event_date: '2025-05-30T20:00:00+03:00',
    city: 'Москва',
    venue: 'Суперметалл',
    description: 'Фестиваль «Возрождение» — масштабное событие, объединяющее лучших артистов электронной сцены России.',
    poster_url: 'https://rupor.events/storage/12109/conversions/01JG4MKMJ7QKH52SA7HV5B1YAZ-poster.webp',
    lineup: ['Sony Vibe', 'Natasha Wax', 'Multiple Artists']
  },
  {
    title: 'Красный Угол',
    event_date: '2025-05-10T23:00:00+03:00',
    city: 'Москва',
    venue: 'Mix Afterparty',
    description: 'Красный Угол — андеграундная вечеринка для истинных ценителей глубокого звука и экспериментальной электроники.',
    poster_url: 'https://rupor.events/storage/16349/conversions/01JTKCE7BJ08CRTFT64JD7E0X8-poster.webp',
    lineup: ['Sony Vibe', 'Underground Artists']
  },
  {
    title: 'Feast of Light',
    event_date: '2025-04-19T22:00:00+03:00',
    city: 'Москва',
    venue: 'Mix Afterparty',
    description: 'Feast of Light — световое шоу в сочетании с мощными битами. Визуальное и музыкальное путешествие.',
    poster_url: 'https://rupor.events/storage/15830/conversions/01JS3YQR06S8ZKGWFCNXWN726Z-poster.webp',
    lineup: ['Sony Vibe', 'Natasha Wax', 'Visual Artists']
  },
  {
    title: 'Fenomen',
    event_date: '2025-04-19T21:30:00+03:00',
    city: 'Москва',
    venue: 'Gestalt',
    description: 'Fenomen — феноменальная вечеринка, где каждый трек становится открытием, а каждый бит — откровением.',
    poster_url: 'https://rupor.events/storage/15703/conversions/01JRXJZZP5RJKMVCBQCFVRYZR6-poster.webp',
    lineup: ['Sony Vibe', 'Natasha Wax']
  },
  {
    title: 'Галерея',
    event_date: '2025-04-18T22:00:00+03:00',
    city: 'Москва',
    venue: 'Blanc',
    description: 'Галерея — где музыка становится искусством. Эксклюзивная вечеринка в атмосфере современного арт-пространства.',
    poster_url: 'https://rupor.events/storage/15720/conversions/01JRYXMBCVQWX6919T4442Q73M-poster.webp',
    lineup: ['Sony Vibe', 'Natasha Wax', 'Art Collective']
  },
  {
    title: 'Natasha Wax B-Day Party',
    event_date: '2024-10-05T23:00:00+03:00',
    city: 'Москва',
    venue: 'Community Moscow',
    description: 'День рождения Natasha Wax — грандиозная вечеринка в честь талантливой артистки с участием лучших друзей сцены.',
    poster_url: 'https://rupor.events/storage/9251/conversions/01J887S31ZT51JETMDP5TBQMZ6-poster.webp',
    lineup: ['Natasha Wax', 'Sony Vibe', 'Birthday Special Guests']
  }
]

async function populateEvents() {
  console.log('🚀 Начинаю заполнение базы данных событиями...')
  
  try {
    // Сначала проверим, есть ли уже события в базе
    const { data: existingEvents, error: checkError } = await supabase
      .from('events')
      .select('id')
      .limit(1)
    
    if (checkError) {
      console.error('❌ Ошибка при проверке существующих событий:', checkError)
      return
    }
    
    if (existingEvents && existingEvents.length > 0) {
      console.log('⚠️  В базе уже есть события. Хотите очистить базу перед добавлением новых? (y/N)')
      // В реальном скрипте можно добавить интерактивный ввод
      console.log('📝 Добавляю события к существующим...')
    }
    
    // Добавляем события одно за другим для лучшего контроля
    let successCount = 0
    let errorCount = 0
    
    for (const eventData of eventsData) {
      try {
        const { error } = await supabase
          .from('events')
          .insert(eventData)
          .select()
          .single()
        
        if (error) {
          console.error(`❌ Ошибка при добавлении события "${eventData.title}":`, error)
          errorCount++
        } else {
          console.log(`✅ Добавлено событие: "${eventData.title}" (${eventData.event_date})`)
          successCount++
        }
      } catch (err) {
        console.error(`❌ Исключение при добавлении события "${eventData.title}":`, err)
        errorCount++
      }
    }
    
    console.log('\n📊 Результаты:')
    console.log(`✅ Успешно добавлено: ${successCount} событий`)
    if (errorCount > 0) {
      console.log(`❌ Ошибок: ${errorCount}`)
    }
    console.log('\n🎉 Заполнение базы данных завершено!')
    
  } catch (error) {
    console.error('❌ Критическая ошибка:', error)
  }
}

// Запускаем скрипт
populateEvents() 
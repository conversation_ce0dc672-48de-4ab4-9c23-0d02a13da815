import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      events: {
        Row: {
          id: string
          title: string
          event_date: string
          city: string
          venue: string
          description: string | null
          poster_url: string | null
          ticket_url: string | null
          lineup: string[] | null
          created_at: string
        }
        Insert: {
          id?: string
          title: string
          event_date: string
          city: string
          venue: string
          description?: string | null
          poster_url?: string | null
          ticket_url?: string | null
          lineup?: string[] | null
          created_at?: string
        }
        Update: {
          id?: string
          title?: string
          event_date?: string
          city?: string
          venue?: string
          description?: string | null
          poster_url?: string | null
          ticket_url?: string | null
          lineup?: string[] | null
          created_at?: string
        }
      }
    }
  }
}

export type Event = Database['public']['Tables']['events']['Row'] 
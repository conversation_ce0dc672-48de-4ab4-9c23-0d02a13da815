"use client"

import { <PERSON>, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { useEffect, useState } from "react"

export function ThemeToggle() {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <button className="w-10 h-10 rounded-lg bg-muted flex items-center justify-center">
        <div className="h-4 w-4" />
      </button>
    )
  }

  return (
    <button
      onClick={() => setTheme(theme === "light" ? "dark" : "light")}
      className="w-10 h-10 rounded-lg bg-muted hover:bg-muted/80 border border-border hover:border-primary/30 transition-all duration-200 flex items-center justify-center group"
    >
      {theme === "light" ? (
        <Moon className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors" />
      ) : (
        <Sun className="h-4 w-4 text-muted-foreground group-hover:text-accent transition-colors" />
      )}
      <span className="sr-only">Переключить тему</span>
    </button>
  )
} 
# Настройка базы данных

## Шаги настройки в Supabase Dashboard

1. **Выполнить SQL-скрипт**
   - Перейти в Supabase Dashboard → SQL Editor
   - Скопировать и выполнить содержимое файла `supabase/migrations/001_initial_schema.sql`

2. **Настроить аутентификацию**
   - Перейти в Authentication → Settings
   - Включить Email provider
   - Отключить "Confirm email" (для magic links)
   - Включить "Enable email confirmations"

3. **Проверить Storage**
   - Перейти в Storage
   - Убедиться, что bucket "posters" создан и публичный

4. **Настроить RLS**
   - Перейти в Database → Tables → events
   - Убедиться, что RLS включен
   - Проверить политики безопасности

## Структура таблицы events

```sql
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    event_date TIMESTAMPTZ NOT NULL,
    city TEXT NOT NULL,
    venue TEXT NOT NULL,
    description TEXT,
    poster_url TEXT,
    ticket_url TEXT,
    lineup TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Политики безопасности

- **Публичный доступ**: Чтение событий доступно всем
- **Аутентифицированные пользователи**: Полный CRUD доступ к событиям
- **Storage**: Публичное чтение постеров, загрузка только для аутентифицированных пользователей 
import { supabase } from './supabase/client'

// Функция для регистрации нового пользователя
export async function signUp(email: string, password: string) {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${window.location.origin}/admin/login?type=recovery`,
    },
  })
  
  if (error) {
    throw error
  }
  
  return data
}

// Функция для входа с email и паролем
export async function signInWithPassword(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })
  
  if (error) {
    throw error
  }

  // Проверяем, что у нас есть пользователь и сессия
  if (!data.user || !data.session) {
    throw new Error('Не удалось создать сессию')
  }
  
  return data
}

// Функция для сброса пароля
export async function resetPassword(email: string) {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/admin/login?type=recovery`,
  })
  
  if (error) {
    throw error
  }
  
  return { success: true }
}

// Функция для обновления пароля (после сброса)
export async function updatePassword(password: string) {
  const { error } = await supabase.auth.updateUser({
    password,
  })
  
  if (error) {
    throw error
  }
  
  return { success: true }
}

// Функция для проверки и установки сессии из URL hash (для сброса пароля)
export async function handleAuthCallback() {
  if (typeof window === 'undefined') return null
  
  const hashParams = new URLSearchParams(window.location.hash.substring(1))
  const access_token = hashParams.get('access_token')
  const refresh_token = hashParams.get('refresh_token')
  const type = hashParams.get('type')
  
  if (access_token && refresh_token) {
    try {
      const { data, error } = await supabase.auth.setSession({
        access_token,
        refresh_token,
      })
      
      if (error) {
        throw error
      }
      
      // Очищаем hash из URL
      window.history.replaceState(null, '', window.location.pathname + window.location.search)
      
      return { session: data.session, type }
    } catch (error) {
      console.error('Error setting session:', error)
      throw error
    }
  }
  
  return null
}

// Функция для проверки текущей сессии
export async function checkSession() {
  const { data: { session }, error } = await supabase.auth.getSession()
  
  if (error) {
    console.error('Error checking session:', error)
    return null
  }
  
  return session
}

// Оставляем старую функцию для совместимости (можно удалить позже)
export async function signInWithEmail(email: string) {
  const { error } = await supabase.auth.signInWithOtp({
    email,
    options: {
      emailRedirectTo: `${window.location.origin}/auth/callback`,
    },
  })
  
  if (error) {
    throw error
  }
  
  return { success: true }
}

export async function signOut() {
  const { error } = await supabase.auth.signOut()
  
  if (error) {
    throw error
  }
  
  return { success: true }
}

export async function getCurrentUser() {
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error) {
    throw error
  }
  
  return user
}

export async function getSession() {
  const { data: { session }, error } = await supabase.auth.getSession()
  
  if (error) {
    throw error
  }
  
  return session
}

// Функция для получения человекочитаемого сообщения об ошибке
export function getErrorMessage(error: Error | { message?: string } | null | undefined): string {
  if (!error?.message) return 'Неизвестная ошибка'
  
  const message = error.message.toLowerCase()
  
  // Ошибки регистрации
  if (message.includes('user already registered')) {
    return 'Пользователь с таким email уже зарегистрирован'
  }
  if (message.includes('password should be at least')) {
    return 'Пароль должен содержать минимум 6 символов'
  }
  if (message.includes('signup is disabled')) {
    return 'Регистрация отключена'
  }
  
  // Ошибки входа
  if (message.includes('invalid login credentials')) {
    return 'Неверный email или пароль'
  }
  if (message.includes('email not confirmed')) {
    return 'Email не подтвержден. Проверьте почту'
  }
  if (message.includes('too many requests')) {
    return 'Слишком много попыток. Попробуйте позже'
  }
  
  // Ошибки сброса пароля
  if (message.includes('unable to validate email address')) {
    return 'Неверный email адрес'
  }
  if (message.includes('email rate limit exceeded')) {
    return 'Слишком много запросов на сброс пароля. Попробуйте позже'
  }
  
  // Общие ошибки
  if (message.includes('network')) {
    return 'Ошибка сети. Проверьте подключение к интернету'
  }
  
  return 'Произошла ошибка. Попробуйте еще раз'
} 
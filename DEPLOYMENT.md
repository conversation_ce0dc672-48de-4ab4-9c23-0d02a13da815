# Деплой на Vercel

## Подготовка к деплою

### 1. Настройка Supabase

Перед деплоем убедитесь, что в Supabase Dashboard выполнены следующие шаги:

1. **Создание таблицы и настройка RLS**
   - Выполните SQL из `supabase/migrations/001_initial_schema.sql`
   - Проверьте, что таблица `events` создана
   - Убедитесь, что RLS политики настроены

2. **Настройка аутентификации**
   - Authentication → Settings → Enable Email provider
   - Отключите "Confirm email" для magic links
   - Добавьте домен сайта в "Site URL" и "Redirect URLs"

3. **Настройка Storage**
   - Bucket "posters" должен быть создан и публичный
   - Политики доступа настроены согласно миграции

### 2. Деплой на Vercel

1. **Подключение репозитория**
   ```bash
   # Если еще не создан git репозиторий
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin <your-repo-url>
   git push -u origin main
   ```

2. **Создание проекта в Vercel**
   - Перейдите на [vercel.com](https://vercel.com)
   - Подключите GitHub репозиторий
   - Выберите Next.js framework preset

3. **Настройка переменных окружения**
   В Vercel Dashboard → Settings → Environment Variables добавьте:
   
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   ```

4. **Деплой**
   - Vercel автоматически задеплоит проект
   - Последующие коммиты будут автоматически деплоиться

### 3. Настройка домена (опционально)

1. **Добавление кастомного домена**
   - Vercel Dashboard → Settings → Domains
   - Добавьте ваш домен
   - Настройте DNS записи согласно инструкциям

2. **Обновление Supabase настроек**
   - Добавьте новый домен в Supabase Authentication settings
   - Обновите "Site URL" и "Redirect URLs"

### 4. Настройка Preview Protection

Для защиты preview-деплоев от индексации:

1. **Vercel Dashboard → Settings → Deployment Protection**
2. **Включите "Vercel Authentication"** для preview deployments
3. **Добавьте пароль** для доступа к preview

### 5. Мониторинг и аналитика

1. **Vercel Analytics**
   - Включите в Vercel Dashboard → Analytics
   - Добавьте `@vercel/analytics` в проект при необходимости

2. **Мониторинг производительности**
   - Используйте Vercel Speed Insights
   - Следите за Core Web Vitals

## Проверка деплоя

После деплоя проверьте:

- ✅ Главная страница загружается < 1 секунды
- ✅ Админ-панель доступна по `/admin/login`
- ✅ Magic link аутентификация работает
- ✅ Создание событий работает корректно
- ✅ Real-time обновления функционируют
- ✅ Загрузка изображений в Storage работает
- ✅ Responsive дизайн на всех устройствах

## Troubleshooting

### Проблема: "relation events does not exist"
**Решение:** Выполните SQL миграцию в Supabase Dashboard

### Проблема: Аутентификация не работает
**Решение:** Проверьте настройки redirect URLs в Supabase

### Проблема: Изображения не загружаются
**Решение:** Проверьте политики RLS для Storage bucket

### Проблема: Real-time не работает
**Решение:** Убедитесь, что в Supabase включен Realtime для таблицы events

## Команды для локальной разработки

```bash
# Запуск в dev режиме
npm run dev

# Сборка для продакшена
npm run build

# Запуск продакшен сборки локально
npm run start

# Проверка типов
npm run type-check

# Линтинг
npm run lint
```

## Структура Environment Variables

```bash
# Обязательные для продакшена
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Опциональные для аналитики
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your-analytics-id
```

## Безопасность

1. **RLS политики** настроены для защиты данных
2. **Middleware** защищает админ-панель
3. **CORS** настроен в Supabase для вашего домена
4. **Rate limiting** можно добавить через Vercel Edge Functions

## Поддержка

При возникновении проблем:
1. Проверьте логи в Vercel Dashboard
2. Проверьте логи в Supabase Dashboard
3. Убедитесь, что все переменные окружения настроены
4. Проверьте статус Supabase сервисов 
import { supabase } from './supabase/client'
import type { Event } from './supabase/client'
import { SupabaseClient } from '@supabase/supabase-js'

export async function getUpcomingEvents(): Promise<Event[]> {
  try {
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .gte('event_date', new Date().toISOString())
      .order('event_date', { ascending: true })
    
    if (error) {
      console.error('Error fetching events:', error)
      return []
    }
    
    return data || []
  } catch (error) {
    console.error('Error fetching events:', error)
    return []
  }
}

export async function getEventById(id: string, client?: SupabaseClient): Promise<Event | null> {
  try {
    const supabaseClient = client || supabase
    const { data, error } = await supabaseClient
      .from('events')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return null // Event not found
      }
      console.error('Error fetching event:', error)
      return null
    }
    
    return data
  } catch (error) {
    console.error('Error fetching event:', error)
    return null
  }
}

export async function getAllEvents(client?: SupabaseClient): Promise<Event[]> {
  const supabaseClient = client || supabase
  const { data, error } = await supabaseClient
    .from('events')
    .select('*')
    .order('event_date', { ascending: false })
  
  if (error) {
    throw error
  }
  
  return data || []
}

export async function createEvent(event: Omit<Event, 'id' | 'created_at'>, client?: SupabaseClient): Promise<Event> {
  const supabaseClient = client || supabase
  const { data, error } = await supabaseClient
    .from('events')
    .insert(event)
    .select()
    .single()
  
  if (error) {
    throw error
  }
  
  return data
}

export async function updateEvent(id: string, updates: Partial<Omit<Event, 'id' | 'created_at'>>, client?: SupabaseClient): Promise<Event> {
  const supabaseClient = client || supabase
  const { data, error } = await supabaseClient
    .from('events')
    .update(updates)
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw error
  }
  
  return data
}

export async function deleteEvent(id: string, client?: SupabaseClient): Promise<void> {
  const supabaseClient = client || supabase
  const { error } = await supabaseClient
    .from('events')
    .delete()
    .eq('id', id)
  
  if (error) {
    throw error
  }
}

export async function uploadPoster(file: File, client?: SupabaseClient): Promise<string> {
  const supabaseClient = client || supabase
  const fileExt = file.name.split('.').pop()
  const fileName = `${Date.now()}.${fileExt}`
  const filePath = `${fileName}`

  const { error: uploadError } = await supabaseClient.storage
    .from('posters')
    .upload(filePath, file)

  if (uploadError) {
    throw uploadError
  }

  const { data } = supabaseClient.storage
    .from('posters')
    .getPublicUrl(filePath)

  return data.publicUrl
}
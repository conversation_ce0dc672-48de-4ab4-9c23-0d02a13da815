import { NextRequest, NextResponse } from 'next/server'
import { uploadPoster } from '@/lib/events'
import { extractEventFromImage } from '@/lib/ai-service'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    // Создаем Supabase клиент для серверной аутентификации
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // Игнорируем ошибки установки cookies в API роутах
            }
          },
        },
      }
    )

    // Проверяем аутентификацию
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Необходима аутентификация' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'Файл не найден' },
        { status: 400 }
      )
    }

    // Проверяем тип файла
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Поддерживаются только изображения' },
        { status: 400 }
      )
    }

    // Проверяем размер файла (максимум 10MB)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'Размер файла не должен превышать 10MB' },
        { status: 400 }
      )
    }

    // Конвертируем файл в base64 для AI обработки
    const arrayBuffer = await file.arrayBuffer()
    const base64 = Buffer.from(arrayBuffer).toString('base64')

    // Загружаем файл в Supabase Storage
    const posterUrl = await uploadPoster(file)

    // Извлекаем информацию о мероприятии из изображения
    const extractedData = await extractEventFromImage(base64)

    return NextResponse.json({
      success: true,
      poster_url: posterUrl,
      base64: base64,
      extracted_data: extractedData,
      message: extractedData 
        ? 'Изображение загружено и обработано успешно'
        : 'Изображение загружено, но не удалось извлечь информацию о мероприятии',
    })
  } catch (error) {
    console.error('Upload API Error:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Ошибка при загрузке файла',
        success: false,
      },
      { status: 500 }
    )
  }
}

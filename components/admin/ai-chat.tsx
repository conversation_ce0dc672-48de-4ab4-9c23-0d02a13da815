'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Upload, Send, Bot, User, Image as ImageIcon, Check, X, Edit } from 'lucide-react'
import { toast } from 'sonner'

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  image?: string
  event_preview?: any
  requires_confirmation?: boolean
}

interface EventPreview {
  title: string
  event_date: string
  city: string
  venue: string
  description?: string
  lineup?: string[]
  ticket_url?: string
  confidence: number
  extracted_from: 'text' | 'image'
}

export function AIChat() {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'Привет! Я AI-ассистент для управления мероприятиями. Могу помочь создать, редактировать или удалить мероприятия. Также могу обработать афиши - просто загрузите изображение!',
      timestamp: new Date(),
    },
  ])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [pendingConfirmation, setPendingConfirmation] = useState<any>(null)
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast.error('Поддерживаются только изображения')
        return
      }
      
      if (file.size > 10 * 1024 * 1024) {
        toast.error('Размер файла не должен превышать 10MB')
        return
      }

      setSelectedImage(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const clearImage = () => {
    setSelectedImage(null)
    setImagePreview(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const sendMessage = async () => {
    if (!inputValue.trim() && !selectedImage) return

    setIsLoading(true)
    
    try {
      let imageBase64: string | undefined
      let posterUrl: string | undefined

      // Если есть изображение, загружаем его в Supabase Storage
      if (selectedImage) {
        try {
          // Сначала загружаем изображение в Supabase Storage
          const formData = new FormData()
          formData.append('file', selectedImage)

          const uploadResponse = await fetch('/api/ai/upload', {
            method: 'POST',
            body: formData,
          })

          if (!uploadResponse.ok) {
            throw new Error('Ошибка при загрузке изображения')
          }

          const uploadResult = await uploadResponse.json()
          if (!uploadResult.success) {
            throw new Error(uploadResult.error || 'Ошибка при загрузке изображения')
          }

          imageBase64 = uploadResult.base64
          posterUrl = uploadResult.poster_url
        } catch (error) {
          console.error('Ошибка при обработке изображения:', error)
          setIsLoading(false)
          return
        }
      }

      // Добавляем сообщение пользователя
      let messageContent = inputValue || 'Загружено изображение'
      if (posterUrl) {
        messageContent += `\n\n[Афиша загружена: ${posterUrl}]`
      }

      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'user',
        content: inputValue || 'Загружено изображение', // Оставляем оригинальный текст для UI
        timestamp: new Date(),
        image: imagePreview || undefined,
      }

      setMessages(prev => [...prev, userMessage])

      // Отправляем запрос к AI
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [...messages, userMessage].map((msg, index) => ({
            role: msg.role,
            content: index === messages.length ? messageContent : msg.content, // Используем расширенное сообщение для последнего
          })),
          imageBase64,
          posterUrl,
        }),
      })

      if (!response.ok) {
        throw new Error('Ошибка при обращении к AI')
      }

      const aiResponse = await response.json()

      // Добавляем ответ AI
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: aiResponse.message,
        timestamp: new Date(),
        event_preview: aiResponse.event_preview,
        requires_confirmation: aiResponse.requires_confirmation,
      }

      setMessages(prev => [...prev, assistantMessage])

      // Если требуется подтверждение, сохраняем данные
      if (aiResponse.requires_confirmation && aiResponse.event_preview) {
        setPendingConfirmation(aiResponse.event_preview)
      }

      // Очищаем форму
      setInputValue('')
      clearImage()
    } catch (error) {
      console.error('Chat error:', error)
      toast.error('Произошла ошибка при отправке сообщения')
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Извините, произошла ошибка. Попробуйте еще раз.',
        timestamp: new Date(),
      }
      
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleConfirmation = async (confirmed: boolean) => {
    if (!pendingConfirmation) return

    setIsLoading(true)

    try {
      if (confirmed) {
        // Отправляем подтверждение создания
        const response = await fetch('/api/ai/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            messages: [
              ...messages,
              {
                role: 'user',
                content: 'Подтверждаю создание мероприятия',
              },
            ],
            confirmed_event: pendingConfirmation,
          }),
        })

        if (!response.ok) {
          throw new Error('Ошибка при создании мероприятия')
        }

        const result = await response.json()
        
        const confirmMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: result.message || 'Мероприятие успешно создано!',
          timestamp: new Date(),
        }

        setMessages(prev => [...prev, confirmMessage])
        toast.success('Мероприятие создано!')
      } else {
        const cancelMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: 'Создание мероприятия отменено. Если хотите внести изменения, опишите что нужно исправить.',
          timestamp: new Date(),
        }

        setMessages(prev => [...prev, cancelMessage])
      }
    } catch (error) {
      console.error('Confirmation error:', error)
      toast.error('Ошибка при обработке подтверждения')
    } finally {
      setPendingConfirmation(null)
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <Card className="h-[70vh] max-h-[600px] min-h-[400px] flex flex-col shadow-lg border-gray-200">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b py-3">
        <CardTitle className="flex items-center gap-2 text-gray-800 text-lg">
          <Bot className="h-5 w-5 text-blue-600" />
          AI Ассистент
          <Badge variant="secondary" className="ml-auto bg-green-100 text-green-700 text-xs">
            Онлайн
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col gap-3 p-4 min-h-0">
        {/* Область сообщений */}
        <div className="flex-1 overflow-y-auto space-y-3 pr-2 min-h-0">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${
                message.role === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              <div
                className={`flex gap-3 max-w-[85%] ${
                  message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                }`}
              >
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 mt-1 ${
                    message.role === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-700'
                  }`}
                >
                  {message.role === 'user' ? (
                    <User className="h-4 w-4" />
                  ) : (
                    <Bot className="h-4 w-4" />
                  )}
                </div>

                <div className="space-y-1 min-w-0 flex-1">
                  <div
                    className={`rounded-lg p-2.5 break-words ${
                      message.role === 'user'
                        ? 'bg-blue-500 text-white ml-auto max-w-[80%]'
                        : 'bg-gray-100 text-gray-900 border'
                    }`}
                  >
                    {message.image && (
                      <div className="mb-2">
                        <img
                          src={message.image}
                          alt="Загруженное изображение"
                          className="max-w-full h-auto rounded max-h-32 object-cover"
                        />
                      </div>
                    )}
                    <div className="whitespace-pre-wrap text-sm leading-relaxed">{message.content}</div>
                  </div>
                  
                  {/* Превью мероприятия */}
                  {message.event_preview && (
                    <div className="bg-white border-2 border-blue-200 rounded-lg p-3 space-y-2 mt-1 shadow-sm">
                      <div className="flex items-start justify-between">
                        <h4 className="font-semibold text-base text-gray-900 leading-tight">{message.event_preview.title}</h4>
                        <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs ml-2 flex-shrink-0">
                          {Math.round(message.event_preview.confidence * 100)}%
                        </Badge>
                      </div>

                      <div className="space-y-1 text-xs text-gray-700">
                        <div><strong className="text-gray-900">Дата:</strong> {formatDate(message.event_preview.event_date)}</div>
                        <div><strong className="text-gray-900">Место:</strong> {message.event_preview.city}, {message.event_preview.venue}</div>
                        {message.event_preview.description && (
                          <div><strong className="text-gray-900">Описание:</strong> {message.event_preview.description}</div>
                        )}
                        {message.event_preview.lineup?.length > 0 && (
                          <div><strong className="text-gray-900">Lineup:</strong> {message.event_preview.lineup.join(', ')}</div>
                        )}
                        {message.event_preview.ticket_url && (
                          <div><strong className="text-gray-900">Билеты:</strong> <a href={message.event_preview.ticket_url} className="text-blue-600 hover:underline" target="_blank" rel="noopener noreferrer">{message.event_preview.ticket_url}</a></div>
                        )}
                      </div>

                      {message.requires_confirmation && pendingConfirmation && (
                        <div className="flex flex-wrap gap-1 pt-2 border-t">
                          <Button
                            size="sm"
                            onClick={() => handleConfirmation(true)}
                            disabled={isLoading}
                            className="flex items-center gap-1 bg-green-600 hover:bg-green-700 text-xs px-2 py-1 h-7"
                          >
                            <Check className="h-3 w-3" />
                            Подтвердить
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleConfirmation(false)}
                            disabled={isLoading}
                            className="flex items-center gap-1 border-red-300 text-red-600 hover:bg-red-50 text-xs px-2 py-1 h-7"
                          >
                            <X className="h-3 w-3" />
                            Отменить
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="flex items-center gap-1 text-gray-600 hover:bg-gray-100 text-xs px-2 py-1 h-7"
                          >
                            <Edit className="h-3 w-3" />
                            Редактировать
                          </Button>
                        </div>
                      )}
                    </div>
                  )}

                  <div className={`text-xs mt-1 ${
                    message.role === 'user' ? 'text-blue-300 text-right' : 'text-gray-500'
                  }`}>
                    {message.timestamp.toLocaleTimeString('ru-RU', {
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </div>
                </div>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>

        <Separator />

        {/* Превью изображения */}
        {imagePreview && (
          <div className="relative bg-gray-50 p-2 rounded-lg border">
            <img
              src={imagePreview}
              alt="Превью"
              className="max-h-20 rounded border shadow-sm"
            />
            <Button
              size="sm"
              variant="destructive"
              className="absolute top-1 right-1 h-5 w-5 p-0"
              onClick={clearImage}
            >
              <X className="h-3 w-3" />
            </Button>
            <p className="text-xs text-gray-600 mt-1">Готово к отправке</p>
          </div>
        )}

        {/* Форма ввода */}
        <div className="flex gap-2 items-end bg-gray-50 p-2 rounded-lg border-t">
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageSelect}
            className="hidden"
          />

          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={isLoading}
            className="flex-shrink-0 h-8 w-8 p-0"
            title="Загрузить изображение"
          >
            <Upload className="h-3 w-3" />
          </Button>

          <div className="flex-1">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Сообщение или загрузите афишу..."
              disabled={isLoading}
              className="w-full h-8 text-sm"
            />
          </div>

          <Button
            onClick={sendMessage}
            disabled={isLoading || (!inputValue.trim() && !selectedImage)}
            className="flex-shrink-0 h-8 px-3"
            title="Отправить сообщение"
            size="sm"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
            ) : (
              <Send className="h-3 w-3" />
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

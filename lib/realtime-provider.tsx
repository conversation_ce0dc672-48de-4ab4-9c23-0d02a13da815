'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { supabase } from './supabase/client'
import { Event } from './supabase/client'

interface RealtimeContextType {
  events: Event[]
  refreshEvents: () => Promise<void>
}

const RealtimeContext = createContext<RealtimeContextType>({
  events: [],
  refreshEvents: async () => {},
})

export const useRealtime = () => useContext(RealtimeContext)

interface RealtimeProviderProps {
  children: React.ReactNode
  initialEvents?: Event[]
}

export function RealtimeProvider({ children, initialEvents = [] }: RealtimeProviderProps) {
  const [events, setEvents] = useState<Event[]>(initialEvents)

  const refreshEvents = async () => {
    try {
      const { data, error } = await supabase
        .from('events')
        .select('*')
        .order('event_date', { ascending: false })

      if (error) {
        console.error('Error refreshing events:', error)
        return
      }

      setEvents(data || [])
    } catch (error) {
      console.error('Error refreshing events:', error)
    }
  }

  useEffect(() => {
    // Real-time подписка на изменения в таблице events
    const channel = supabase
      .channel('events_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'events',
        },
        (payload) => {
          console.log('Real-time event:', payload)
          // Обновляем список событий при любых изменениях
          refreshEvents()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [])

  return (
    <RealtimeContext.Provider value={{ events, refreshEvents }}>
      {children}
    </RealtimeContext.Provider>
  )
} 
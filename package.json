{"name": "a<PERSON>a", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup-database": "node scripts/setup-database.mjs", "populate-events": "node scripts/populate-events.mjs", "clear-events": "node scripts/clear-events.mjs"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-toast": "^1.2.4", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.47.10", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.1.0", "lucide-react": "^0.525.0", "next": "15.3.5", "next-themes": "^0.4.6", "react": "19.0.0", "react-dom": "19.0.0", "sonner": "^1.7.4", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@netlify/plugin-nextjs": "^5.11.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8", "typescript": "^5"}}
import { getAllEvents } from '@/lib/events'
import { RealtimeProvider } from '@/lib/realtime-provider'
import { EventsList } from '@/components/events-list'
import { Event } from '@/lib/supabase/client'
import { ThemeToggle } from '@/components/theme-toggle'
import Link from 'next/link'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Natasha Wax & Sony Vibe - Официальная афиша',
  description: 'Официальная афиша техно-дуэта Natasha Wax & Sony Vibe. Концерты, DJ-сеты, фестивали и эксклюзивные мероприятия электронной музыки.',
  openGraph: {
    title: 'Natasha Wax & Sony Vibe - Официальная афиша',
    description: 'Официальная афиша техно-дуэта Natasha Wax & Sony Vibe. Концерты, DJ-сеты, фестивали.',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: '<PERSON> Wax & Sony Vibe',
      },
    ],
  },
}

export default async function HomePage() {
  // Получаем все события (включая прошедшие)
  let initialEvents: Event[] = []
  try {
    initialEvents = await getAllEvents()
  } catch (error) {
    console.error('Failed to load events:', error)
  }

  return (
    <RealtimeProvider initialEvents={initialEvents}>
      <div className="min-h-screen bg-background">
        {/* Минималистичный header */}
        <header className="border-b border-border/30 bg-background/80 backdrop-blur-md">
          <div className="container mx-auto px-4 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-8">
                <h1 className="brand-logo text-2xl md:text-3xl font-bold tracking-tight">
                  NATASHA WAX & SONY VIBE
                </h1>
                <div className="hidden md:block">
                  <span className="text-muted-foreground/80 text-sm font-medium tracking-wider uppercase">
                    Electronic • Techno
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <ThemeToggle />
                <Link
                  href="/admin/login"
                  className="text-xs font-medium px-3 py-1.5 rounded-md bg-muted/50 hover:bg-muted/80 transition-colors opacity-60 hover:opacity-100"
                >
                  Админ
                </Link>
              </div>
            </div>
          </div>
        </header>

        {/* Сразу события без лишних блоков */}
        <main className="container mx-auto px-4 py-8">
          <EventsList />
        </main>

        {/* Минимальный footer */}
        <footer className="border-t border-border/30 bg-muted/20 mt-20">
          <div className="container mx-auto px-4 py-12">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <h3 className="brand-logo text-lg font-bold mb-3">NATASHA WAX & SONY VIBE</h3>
                <p className="text-muted-foreground text-sm leading-relaxed">
                Диджейский дуэт тех-хауса и мелодик-техно, основательницы проекта и лейбла 16om, известны своими энергичными выступлениями на клубных сценах и фестивалях по всему миру.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-3 accent-text text-sm">Стиль</h4>
                <div className="space-y-1 text-muted-foreground text-xs">
                  <p>Deep Techno</p>
                  <p>Melodic Electronic</p>
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-3 accent-text text-sm">Контакты</h4>
                <div className="space-y-1 text-muted-foreground text-xs">
                  <p><EMAIL></p>
                  <p>@natashawax_sonyvibe</p>
                </div>
              </div>
            </div>
            <div className="border-t border-border/30 mt-8 pt-6 text-center">
              <p className="text-muted-foreground text-xs">
                © 2025 Natasha Wax & Sony Vibe
              </p>
            </div>
          </div>
        </footer>
      </div>
    </RealtimeProvider>
  )
}

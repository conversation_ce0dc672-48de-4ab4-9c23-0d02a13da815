#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Читаем .env.local файл
function loadEnvLocal() {
  const envPath = path.join(__dirname, '..', '.env.local')
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8')
    const lines = envContent.split('\n')
    
    for (const line of lines) {
      const trimmedLine = line.trim()
      if (trimmedLine && !trimmedLine.startsWith('#') && trimmedLine.includes('=')) {
        const [key, ...valueParts] = trimmedLine.split('=')
        const value = valueParts.join('=')
        process.env[key] = value
      }
    }
  }
}

// Загружаем переменные окружения
loadEnvLocal()

// Получаем переменные окружения
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

console.log('🔍 Проверка переменных окружения...')
console.log('URL:', supabaseUrl ? '✅ Найден' : '❌ Не найден')
console.log('Service Key:', supabaseServiceKey ? '✅ Найден' : '❌ Не найден')

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Ошибка: Не найдены переменные окружения')
  console.log('Убедитесь, что в .env.local установлены:')
  console.log('- NEXT_PUBLIC_SUPABASE_URL')
  console.log('- SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Создаем админ-клиент с Service Role Key
const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupDatabase() {
  console.log('🚀 Начинаем настройку Supabase...')

  try {
    // 1. Создаем таблицу events через отдельные запросы
    console.log('📊 Создание таблицы events...')
    
    // Включаем расширение UUID
    const { error: extError } = await supabase.rpc('exec_sql', {
      sql: 'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";'
    })
    
    if (extError) {
      console.log('⚠️ UUID extension:', extError.message)
    } else {
      console.log('✅ UUID extension включен')
    }

    // Создаем таблицу
    const { error: tableError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS events (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            title TEXT NOT NULL,
            event_date TIMESTAMPTZ NOT NULL,
            city TEXT NOT NULL,
            venue TEXT NOT NULL,
            description TEXT,
            poster_url TEXT,
            ticket_url TEXT,
            lineup TEXT[],
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
      `
    })

    if (tableError) {
      console.error('❌ Ошибка создания таблицы:', tableError.message)
    } else {
      console.log('✅ Таблица events создана успешно')
    }

    // Создаем индексы
    const { error: indexError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE INDEX IF NOT EXISTS idx_events_event_date ON events(event_date);
        CREATE INDEX IF NOT EXISTS idx_events_city ON events(city);
      `
    })

    if (indexError) {
      console.log('⚠️ Индексы:', indexError.message)
    } else {
      console.log('✅ Индексы созданы')
    }

    // 2. Настраиваем RLS
    console.log('🔒 Настройка Row Level Security...')
    
    // Включаем RLS
    const { error: rlsEnableError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE events ENABLE ROW LEVEL SECURITY;'
    })

    if (rlsEnableError) {
      console.log('⚠️ RLS enable:', rlsEnableError.message)
    } else {
      console.log('✅ RLS включен')
    }

    // Создаем политики по одной
    const policies = [
      {
        name: 'Public read access',
        sql: 'CREATE POLICY "Public read access" ON events FOR SELECT USING (true);'
      },
      {
        name: 'Authenticated insert',
        sql: 'CREATE POLICY "Authenticated users can insert" ON events FOR INSERT WITH CHECK (auth.role() = \'authenticated\');'
      },
      {
        name: 'Authenticated update', 
        sql: 'CREATE POLICY "Authenticated users can update" ON events FOR UPDATE USING (auth.role() = \'authenticated\');'
      },
      {
        name: 'Authenticated delete',
        sql: 'CREATE POLICY "Authenticated users can delete" ON events FOR DELETE USING (auth.role() = \'authenticated\');'
      }
    ]

    for (const policy of policies) {
      // Сначала удаляем если существует
      await supabase.rpc('exec_sql', {
        sql: `DROP POLICY IF EXISTS "${policy.name}" ON events;`
      })
      
      // Создаем новую
      const { error: policyError } = await supabase.rpc('exec_sql', {
        sql: policy.sql
      })

      if (policyError) {
        console.log(`⚠️ ${policy.name}:`, policyError.message)
      } else {
        console.log(`✅ ${policy.name} создана`)
      }
    }

    // 3. Создаем storage bucket
    console.log('📁 Создание storage bucket...')
    
    const { error: bucketError } = await supabase.storage.createBucket('posters', {
      public: true,
      allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
      fileSizeLimit: 5242880 // 5MB
    })

    if (bucketError && !bucketError.message.includes('already exists')) {
      console.log('⚠️ Storage bucket:', bucketError.message)
    } else {
      console.log('✅ Storage bucket готов')
    }

    // 4. Проверяем подключение
    console.log('🔍 Проверка подключения...')
    
    const { data, error: testError } = await supabase
      .from('events')
      .select('count')
      .limit(1)

    if (testError) {
      console.log('⚠️ Тест подключения:', testError.message)
    } else {
      console.log('✅ Подключение к базе данных работает')
    }

    console.log('\n🎉 Настройка Supabase завершена!')
    console.log('\n📋 Следующие шаги:')
    console.log('1. Настройте аутентификацию в Supabase Dashboard:')
    console.log('   - Authentication → Settings → Email → Enable')
    console.log('   - Отключите "Confirm email"')
    console.log('   - Добавьте домен в "Site URL"')
    console.log('2. Перезапустите dev сервер: npm run dev')
    console.log('3. Перейдите на /admin/login для входа')
    console.log('4. Создайте первое событие')

  } catch (error) {
    console.error('❌ Критическая ошибка:', error.message)
    process.exit(1)
  }
}

// Запускаем настройку
if (require.main === module) {
  setupDatabase()
}

module.exports = { setupDatabase } 
import { getAllEvents, getEventById, createEvent, updateEvent, deleteEvent } from './events'
import { Event } from './supabase/client'
import { EventPreview } from './ai-service'
import { SupabaseClient } from '@supabase/supabase-js'

export interface FunctionCallResult {
  success: boolean
  data?: any
  error?: string
  message?: string
}

// Обработчик вызовов функций от AI
export async function handleFunctionCall(
  functionName: string,
  args: any,
  supabase?: SupabaseClient
): Promise<FunctionCallResult> {
  try {
    switch (functionName) {
      case 'get_events':
        return await handleGetEvents(supabase)

      case 'get_event_by_id':
        return await handleGetEventById(args.id, supabase)

      case 'create_event_preview':
        return await handleCreateEventPreview(args)

      case 'create_event':
        return await handleCreateEvent(args, supabase)

      case 'update_event':
        return await handleUpdateEvent(args, supabase)

      case 'delete_event':
        return await handleDeleteEvent(args.id, supabase)
      
      default:
        return {
          success: false,
          error: `Неизвестная функция: ${functionName}`,
        }
    }
  } catch (error) {
    console.error(`Error in function ${functionName}:`, error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Неизвестная ошибка',
    }
  }
}

// Получить все мероприятия
async function handleGetEvents(supabase?: SupabaseClient): Promise<FunctionCallResult> {
  try {
    const events = await getAllEvents(supabase)
    return {
      success: true,
      data: events,
      message: `Найдено ${events.length} мероприятий`,
    }
  } catch (error) {
    return {
      success: false,
      error: 'Ошибка при получении списка мероприятий',
    }
  }
}

// Получить мероприятие по ID
async function handleGetEventById(id: string, supabase?: SupabaseClient): Promise<FunctionCallResult> {
  if (!id) {
    return {
      success: false,
      error: 'ID мероприятия не указан',
    }
  }

  try {
    const event = await getEventById(id, supabase)
    if (!event) {
      return {
        success: false,
        error: 'Мероприятие не найдено',
      }
    }

    return {
      success: true,
      data: event,
      message: `Мероприятие "${event.title}" найдено`,
    }
  } catch (error) {
    return {
      success: false,
      error: 'Ошибка при получении мероприятия',
    }
  }
}

// Создать превью мероприятия (для подтверждения)
async function handleCreateEventPreview(args: any): Promise<FunctionCallResult> {
  const { title, event_date, city, venue, description, lineup, ticket_url } = args

  // Валидация обязательных полей
  if (!title || !event_date || !city || !venue) {
    return {
      success: false,
      error: 'Не указаны обязательные поля: название, дата, город, место проведения',
    }
  }

  // Валидация даты
  const eventDate = new Date(event_date)
  if (isNaN(eventDate.getTime())) {
    return {
      success: false,
      error: 'Неверный формат даты. Используйте формат ISO 8601',
    }
  }

  const preview: EventPreview = {
    title,
    event_date,
    city,
    venue,
    description: description || undefined,
    lineup: lineup || undefined,
    ticket_url: ticket_url || undefined,
    confidence: 0.9,
    extracted_from: 'text',
  }

  return {
    success: true,
    data: preview,
    message: 'Превью мероприятия создано. Подтвердите создание.',
  }
}

// Создать мероприятие
async function handleCreateEvent(args: any, supabase?: SupabaseClient): Promise<FunctionCallResult> {
  const { title, event_date, city, venue, description, lineup, ticket_url, poster_url } = args

  // Валидация обязательных полей
  if (!title || !event_date || !city || !venue) {
    return {
      success: false,
      error: 'Не указаны обязательные поля: название, дата, город, место проведения',
    }
  }

  // Валидация даты
  const eventDate = new Date(event_date)
  if (isNaN(eventDate.getTime())) {
    return {
      success: false,
      error: 'Неверный формат даты. Используйте формат ISO 8601',
    }
  }

  try {
    const eventData: Omit<Event, 'id' | 'created_at'> = {
      title,
      event_date,
      city,
      venue,
      description: description || null,
      lineup: lineup || null,
      ticket_url: ticket_url || null,
      poster_url: poster_url || null,
    }

    const newEvent = await createEvent(eventData, supabase)
    
    return {
      success: true,
      data: newEvent,
      message: `Мероприятие "${newEvent.title}" успешно создано!`,
    }
  } catch (error) {
    return {
      success: false,
      error: 'Ошибка при создании мероприятия',
    }
  }
}

// Обновить мероприятие
async function handleUpdateEvent(args: any, supabase?: SupabaseClient): Promise<FunctionCallResult> {
  console.log('Update event args:', args)
  const { id, ...updates } = args

  if (!id) {
    return {
      success: false,
      error: 'ID мероприятия не указан',
    }
  }

  console.log('Updating event with ID:', id, 'Updates:', updates)

  // Проверяем, существует ли мероприятие
  try {
    const existingEvent = await getEventById(id, supabase)
    if (!existingEvent) {
      return {
        success: false,
        error: 'Мероприятие не найдено',
      }
    }

    // Валидация даты, если она обновляется
    if (updates.event_date) {
      const eventDate = new Date(updates.event_date)
      if (isNaN(eventDate.getTime())) {
        return {
          success: false,
          error: 'Неверный формат даты. Используйте формат ISO 8601',
        }
      }
    }

    // Фильтруем undefined значения
    const filteredUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    )

    const updatedEvent = await updateEvent(id, filteredUpdates, supabase)
    
    return {
      success: true,
      data: updatedEvent,
      message: `Мероприятие "${updatedEvent.title}" успешно обновлено!`,
    }
  } catch (error) {
    console.error('Update event error:', error)
    return {
      success: false,
      error: `Ошибка при обновлении мероприятия: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`,
    }
  }
}

// Удалить мероприятие
async function handleDeleteEvent(id: string, supabase?: SupabaseClient): Promise<FunctionCallResult> {
  if (!id) {
    return {
      success: false,
      error: 'ID мероприятия не указан',
    }
  }

  try {
    // Проверяем, существует ли мероприятие
    const existingEvent = await getEventById(id, supabase)
    if (!existingEvent) {
      return {
        success: false,
        error: 'Мероприятие не найдено',
      }
    }

    await deleteEvent(id, supabase)
    
    return {
      success: true,
      message: `Мероприятие "${existingEvent.title}" успешно удалено!`,
    }
  } catch (error) {
    return {
      success: false,
      error: 'Ошибка при удалении мероприятия',
    }
  }
}

// Утилита для форматирования списка мероприятий для AI
export function formatEventsForAI(events: Event[]): string {
  if (events.length === 0) {
    return 'Мероприятий не найдено.'
  }

  return events
    .map((event, index) => {
      const date = new Date(event.event_date).toLocaleString('ru-RU', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      })

      return `${index + 1}. "${event.title}"
   ID: ${event.id}
   Дата: ${date}
   Место: ${event.city}, ${event.venue}
   ${event.description ? `Описание: ${event.description}` : ''}
   ${event.lineup?.length ? `Lineup: ${event.lineup.join(', ')}` : ''}
   ${event.ticket_url ? `Билеты: ${event.ticket_url}` : ''}`
    })
    .join('\n\n')
}

// Утилита для форматирования одного мероприятия для AI
export function formatEventForAI(event: Event): string {
  const date = new Date(event.event_date).toLocaleString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })

  return `Мероприятие "${event.title}":
ID: ${event.id}
Дата: ${date}
Место: ${event.city}, ${event.venue}
${event.description ? `Описание: ${event.description}` : ''}
${event.lineup?.length ? `Lineup: ${event.lineup.join(', ')}` : ''}
${event.ticket_url ? `Билеты: ${event.ticket_url}` : ''}
${event.poster_url ? `Постер: ${event.poster_url}` : ''}
Создано: ${new Date(event.created_at).toLocaleString('ru-RU')}`
}

// Валидация данных мероприятия
export function validateEventData(data: Partial<Event>): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!data.title?.trim()) {
    errors.push('Название мероприятия обязательно')
  }

  if (!data.event_date) {
    errors.push('Дата мероприятия обязательна')
  } else {
    const eventDate = new Date(data.event_date)
    if (isNaN(eventDate.getTime())) {
      errors.push('Неверный формат даты')
    }
  }

  if (!data.city?.trim()) {
    errors.push('Город обязателен')
  }

  if (!data.venue?.trim()) {
    errors.push('Место проведения обязательно')
  }

  if (data.ticket_url && !isValidUrl(data.ticket_url)) {
    errors.push('Неверный формат URL для билетов')
  }

  if (data.poster_url && !isValidUrl(data.poster_url)) {
    errors.push('Неверный формат URL для постера')
  }

  return {
    valid: errors.length === 0,
    errors,
  }
}

// Утилита для проверки URL
function isValidUrl(string: string): boolean {
  try {
    new URL(string)
    return true
  } catch (_) {
    return false
  }
}

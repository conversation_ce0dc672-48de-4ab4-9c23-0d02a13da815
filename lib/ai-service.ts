import OpenAI from 'openai'
import { Event } from './supabase/client'

// Инициализация OpenAI клиента с кастомными настройками
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
  baseURL: process.env.OPENAI_BASE_URL!,
})

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  image?: string
}

export interface EventPreview {
  title: string
  event_date: string
  city: string
  venue: string
  description?: string
  lineup?: string[]
  ticket_url?: string
  poster_url?: string
  confidence: number
  extracted_from: 'text' | 'image'
}

export interface AIResponse {
  message: string
  action?: 'create_event' | 'update_event' | 'delete_event' | 'list_events' | 'get_event'
  event_preview?: EventPreview
  event_id?: string
  events_list?: Event[]
  requires_confirmation?: boolean
}

// Системный промпт для AI ассистента
const SYSTEM_PROMPT = `Ты - AI ассистент для управления мероприятиями в админ-панели сайта "Natasha Wax & Sony Vibe".

ТЕКУЩАЯ ДАТА: ${new Date().toLocaleDateString('ru-RU', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  weekday: 'long'
})} (${new Date().getFullYear()} год)

ТВОИ ВОЗМОЖНОСТИ:
1. Создание новых мероприятий из текста или изображений афиш
2. Просмотр существующих мероприятий
3. Редактирование мероприятий
4. Удаление мероприятий

СТРУКТУРА МЕРОПРИЯТИЯ:
- title (название) - обязательно
- event_date (дата и время) - обязательно, формат ISO 8601
- city (город) - обязательно
- venue (место проведения) - обязательно
- description (описание) - опционально
- lineup (состав исполнителей) - опционально, массив строк
- ticket_url (ссылка на билеты) - опционально

ПРАВИЛА РАБОТЫ:
1. Всегда запрашивай подтверждение перед созданием, изменением или удалением мероприятий
2. При обработке изображений афиш извлекай всю доступную информацию И ОБЯЗАТЕЛЬНО сохраняй изображение как poster_url
3. Если информация неполная, запрашивай недостающие данные у пользователя
4. Используй дружелюбный и профессиональный тон
5. Отвечай на русском языке
6. ВАЖНО: Если на афише не указан год, используй текущий год (${new Date().getFullYear()})
7. ВАЖНО: Если пользователь указывает на ошибку в уже созданном мероприятии, используй update_event, а НЕ создавай новое
8. ВАЖНО: Перед созданием нового мероприятия проверь, нет ли уже похожего события в списке

ДОСТУПНЫЕ ФУНКЦИИ:
- get_events: получить список всех мероприятий
- get_event_by_id: получить мероприятие по ID
- create_event_preview: создать превью мероприятия для подтверждения
- create_event: создать мероприятие после подтверждения
- update_event: обновить существующее мероприятие
- delete_event: удалить мероприятие`

// Функции для работы с мероприятиями (будут переданы в OpenAI)
export const AI_FUNCTIONS = [
  {
    name: 'get_events',
    description: 'Получить список всех мероприятий',
    parameters: {
      type: 'object',
      properties: {},
    },
  },
  {
    name: 'get_event_by_id',
    description: 'Получить мероприятие по ID',
    parameters: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'ID мероприятия',
        },
      },
      required: ['id'],
    },
  },
  {
    name: 'create_event_preview',
    description: 'Создать превью мероприятия для подтверждения пользователем',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Название мероприятия',
        },
        event_date: {
          type: 'string',
          description: 'Дата и время мероприятия в формате ISO 8601',
        },
        city: {
          type: 'string',
          description: 'Город проведения',
        },
        venue: {
          type: 'string',
          description: 'Место проведения',
        },
        description: {
          type: 'string',
          description: 'Описание мероприятия',
        },
        lineup: {
          type: 'array',
          items: { type: 'string' },
          description: 'Список исполнителей',
        },
        ticket_url: {
          type: 'string',
          description: 'Ссылка на билеты',
        },
      },
      required: ['title', 'event_date', 'city', 'venue'],
    },
  },
  {
    name: 'create_event',
    description: 'Создать мероприятие после подтверждения пользователем',
    parameters: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Название мероприятия',
        },
        event_date: {
          type: 'string',
          description: 'Дата и время мероприятия в формате ISO 8601',
        },
        city: {
          type: 'string',
          description: 'Город проведения',
        },
        venue: {
          type: 'string',
          description: 'Место проведения',
        },
        description: {
          type: 'string',
          description: 'Описание мероприятия',
        },
        lineup: {
          type: 'array',
          items: { type: 'string' },
          description: 'Список исполнителей',
        },
        ticket_url: {
          type: 'string',
          description: 'Ссылка на билеты',
        },
        poster_url: {
          type: 'string',
          description: 'URL загруженного постера',
        },
      },
      required: ['title', 'event_date', 'city', 'venue'],
    },
  },
  {
    name: 'update_event',
    description: 'Обновить существующее мероприятие',
    parameters: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'ID мероприятия для обновления',
        },
        title: {
          type: 'string',
          description: 'Название мероприятия',
        },
        event_date: {
          type: 'string',
          description: 'Дата и время мероприятия в формате ISO 8601',
        },
        city: {
          type: 'string',
          description: 'Город проведения',
        },
        venue: {
          type: 'string',
          description: 'Место проведения',
        },
        description: {
          type: 'string',
          description: 'Описание мероприятия',
        },
        lineup: {
          type: 'array',
          items: { type: 'string' },
          description: 'Список исполнителей',
        },
        ticket_url: {
          type: 'string',
          description: 'Ссылка на билеты',
        },
        poster_url: {
          type: 'string',
          description: 'URL постера',
        },
      },
      required: ['id'],
    },
  },
  {
    name: 'delete_event',
    description: 'Удалить мероприятие',
    parameters: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'ID мероприятия для удаления',
        },
      },
      required: ['id'],
    },
  },
]

// Основная функция для общения с AI
export async function chatWithAI(
  messages: ChatMessage[],
  imageBase64?: string,
  posterUrl?: string
): Promise<AIResponse> {
  try {
    // Подготавливаем сообщения для OpenAI
    const openaiMessages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
      { role: 'system', content: SYSTEM_PROMPT },
      ...messages.map((msg) => {
        if (msg.role === 'user' && imageBase64) {
          return {
            role: 'user' as const,
            content: [
              { type: 'text', text: msg.content },
              {
                type: 'image_url',
                image_url: { url: `data:image/jpeg;base64,${imageBase64}` },
              },
            ],
          }
        }
        return { role: msg.role, content: msg.content }
      }),
    ]

    const response = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4-1106-preview',
      messages: openaiMessages,
      tools: AI_FUNCTIONS.map(func => ({
        type: 'function' as const,
        function: func,
      })),
      tool_choice: 'auto',
      temperature: 0.7,
      max_tokens: 2000,
    })

    const choice = response.choices[0]
    const message = choice.message

    // Если AI вызвал функцию
    if (message.tool_calls && message.tool_calls.length > 0) {
      const toolCall = message.tool_calls[0]
      const functionName = toolCall.function.name
      const functionArgs = JSON.parse(toolCall.function.arguments || '{}')

      return {
        message: message.content || '',
        action: functionName as AIResponse['action'],
        event_preview: functionName === 'create_event_preview' ? {
          ...functionArgs,
          poster_url: posterUrl,
          confidence: 0.9,
          extracted_from: imageBase64 ? 'image' : 'text'
        } : undefined,
        event_id: functionArgs.id,
        requires_confirmation: ['create_event_preview', 'create_event', 'update_event', 'delete_event'].includes(functionName),
      }
    }

    return {
      message: message.content || 'Извините, произошла ошибка при обработке запроса.',
    }
  } catch (error) {
    console.error('AI Service Error:', error)
    return {
      message: 'Произошла ошибка при обращении к AI сервису. Попробуйте еще раз.',
    }
  }
}

// Функция для извлечения данных из изображения афиши
export async function extractEventFromImage(imageBase64: string): Promise<EventPreview | null> {
  try {
    const response = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4-vision-preview',
      messages: [
        {
          role: 'system',
          content: `Ты эксперт по извлечению информации о мероприятиях из афиш. 
          Извлеки следующую информацию из изображения:
          - Название мероприятия
          - Дата и время (преобразуй в формат ISO 8601)
          - Город
          - Место проведения
          - Описание (если есть)
          - Состав исполнителей (если есть)
          - Ссылка на билеты (если есть)
          
          Отвечай только в формате JSON без дополнительного текста.`,
        },
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: 'Извлеки информацию о мероприятии из этой афиши:',
            },
            {
              type: 'image_url',
              image_url: { url: `data:image/jpeg;base64,${imageBase64}` },
            },
          ],
        },
      ],
      temperature: 0.3,
      max_tokens: 1000,
    })

    const content = response.choices[0]?.message?.content
    if (!content) return null

    const extracted = JSON.parse(content)
    return {
      ...extracted,
      confidence: 0.85,
      extracted_from: 'image' as const,
    }
  } catch (error) {
    console.error('Image extraction error:', error)
    return null
  }
}

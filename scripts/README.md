# Скрипты управления базой данных

Этот каталог содержит скрипты для настройки и управления базой данных событий.

## Доступные скрипты

### 1. setup-database.mjs
Настройка базы данных и создание начальной структуры.

```bash
npm run setup-database
```

### 2. populate-events.mjs
Заполнение базы данных реальными событиями дуэта Natasha Wax & Sony Vibe.

```bash
npm run populate-events
```

**Что делает:**
- Добавляет 10 реальных событий с постерами
- Включает описания, лайнапы и даты
- Использует прямые ссылки на постеры с rupor.events
- Проверяет существующие события перед добавлением

**Данные включают:**
- Sony Vibe Happy Birthday (16 мая 2025)
- Natasha Wax & Sony Vibe (24 мая 2025)
- TRIP 2025 Official Pre-Party (11 июня 2025)
- Freak Boutique (6 июня 2025)
- «Возрождение» фестиваль (30 мая 2025)
- Красный Угол (10 мая 2025)
- Feast of Light (19 апреля 2025)
- Fenomen (19 апреля 2025)
- Галерея (18 апреля 2025)
- Natasha Wax B-Day Party (5 октября 2024)

### 3. clear-events.mjs
Очистка всех событий из базы данных.

```bash
npm run clear-events
```

**Осторожно:** Этот скрипт удаляет ВСЕ события из базы данных без возможности восстановления.

## Требования

- Файл `.env.local` должен содержать:
  - `NEXT_PUBLIC_SUPABASE_URL`
  - `SUPABASE_SERVICE_ROLE_KEY`

## Примечания

- Скрипты используют service role key для обхода Row Level Security
- Все скрипты работают с производственной базой данных Supabase
- Постеры загружаются по прямым ссылкам (не в storage bucket)
- События добавляются с полной информацией: название, дата, место, описание, лайнап 
import { NextRequest, NextResponse } from 'next/server'
import { chatWithAI, ChatMessage } from '@/lib/ai-service'
import { handleFunctionCall, formatEventsForAI, formatEventForAI } from '@/lib/ai-tools'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    // Создаем Supabase клиент для серверной аутентификации
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              )
            } catch {
              // Игнорируем ошибки установки cookies в API роутах
            }
          },
        },
      }
    )

    // Проверяем аутентификацию
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Необходима аутентификация' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { messages, imageBase64, confirmed_event, posterUrl } = body

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Неверный формат сообщений' },
        { status: 400 }
      )
    }

    // Если есть подтвержденное мероприятие, создаем его
    if (confirmed_event) {
      const functionResult = await handleFunctionCall('create_event', {
        ...confirmed_event,
        poster_url: posterUrl,
      })

      return NextResponse.json({
        message: functionResult.success
          ? `Мероприятие "${confirmed_event.title}" успешно создано!`
          : `Ошибка: ${functionResult.error}`,
        function_result: functionResult,
      })
    }

    // Получаем ответ от AI
    const aiResponse = await chatWithAI(messages as ChatMessage[], imageBase64)

    // Если AI вызвал функцию, выполняем её
    if (aiResponse.action) {
      const functionResult = await handleFunctionCall(
        aiResponse.action,
        aiResponse.event_preview || { id: aiResponse.event_id }
      )

      // Формируем ответ в зависимости от результата
      let responseMessage = aiResponse.message

      if (functionResult.success) {
        switch (aiResponse.action) {
          case 'get_events':
            responseMessage = `${functionResult.message}\n\n${formatEventsForAI(functionResult.data)}`
            aiResponse.events_list = functionResult.data
            break
          
          case 'get_event_by_id':
            responseMessage = `${functionResult.message}\n\n${formatEventForAI(functionResult.data)}`
            break
          
          case 'create_event_preview':
            responseMessage = `Я создал превью мероприятия на основе предоставленной информации. Пожалуйста, проверьте данные и подтвердите создание:\n\n${formatEventPreview(functionResult.data)}`
            aiResponse.event_preview = functionResult.data
            aiResponse.requires_confirmation = true
            break
          
          case 'create_event':
          case 'update_event':
          case 'delete_event':
            responseMessage = functionResult.message || 'Операция выполнена успешно'
            break
        }
      } else {
        responseMessage = `Ошибка: ${functionResult.error}`
      }

      return NextResponse.json({
        ...aiResponse,
        message: responseMessage,
        function_result: functionResult,
      })
    }

    return NextResponse.json(aiResponse)
  } catch (error) {
    console.error('AI Chat API Error:', error)
    return NextResponse.json(
      { error: 'Внутренняя ошибка сервера' },
      { status: 500 }
    )
  }
}

// Утилита для форматирования превью мероприятия
function formatEventPreview(preview: any): string {
  const date = new Date(preview.event_date).toLocaleString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })

  return `📅 **${preview.title}**
🗓️ Дата: ${date}
📍 Место: ${preview.city}, ${preview.venue}
${preview.description ? `📝 Описание: ${preview.description}` : ''}
${preview.lineup?.length ? `🎵 Lineup: ${preview.lineup.join(', ')}` : ''}
${preview.ticket_url ? `🎫 Билеты: ${preview.ticket_url}` : ''}
${preview.confidence ? `🎯 Уверенность: ${Math.round(preview.confidence * 100)}%` : ''}
${preview.extracted_from ? `📊 Источник: ${preview.extracted_from === 'image' ? 'изображение' : 'текст'}` : ''}`
}

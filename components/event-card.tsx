import Link from 'next/link'
import Image from 'next/image'
import { Event } from '@/lib/supabase/client'
import { format } from 'date-fns'
import { ru } from 'date-fns/locale'

interface EventCardProps {
  event: Event
  isPast?: boolean
}

export function EventCard({ event, isPast = false }: EventCardProps) {
  const eventDate = new Date(event.event_date)
  const dayMonth = format(eventDate, 'dd.MM', { locale: ru })
  const time = format(eventDate, 'HH:mm', { locale: ru })
  const weekday = format(eventDate, 'EEEE', { locale: ru })

  return (
    <div className={`event-card p-8 cursor-pointer group relative ${isPast ? 'opacity-50' : ''}`}>
      {/* Основная ссылка на событие (невидимая, покрывает всю карточку) */}
      <Link 
        href={`/event/${event.id}`}
        className="absolute inset-0 z-10"
        aria-label={`Перейти к событию: ${event.title}`}
      />
      
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Дата */}
        <div className="flex-shrink-0 text-center lg:text-left">
          <div className="event-date">{dayMonth}</div>
          <div className="text-muted-foreground text-base capitalize font-medium mt-2">
            {weekday}
          </div>
          <div className="accent-text text-base font-semibold mt-1">
            {time}
          </div>
          {isPast && (
            <div className="mt-3">
              <span className="text-sm tag-accent px-3 py-1 rounded-full">
                Прошло
              </span>
            </div>
          )}
        </div>

        {/* Основная информация */}
        <div className="flex-1 space-y-6">
          <div>
            <h3 className="event-title mb-3">{event.title}</h3>
            <p className="event-venue flex items-center gap-3 text-base">
              <span className="w-2 h-2 bg-primary rounded-full"></span>
              {event.city} • {event.venue}
            </p>
          </div>

          {event.lineup && event.lineup.length > 0 && (
            <div>
              <p className="text-base text-foreground mb-3 font-semibold accent-text">
                Lineup
              </p>
              <div className="flex flex-wrap gap-3">
                {event.lineup.slice(0, 6).map((artist, index) => (
                  <span
                    key={index}
                    className="text-sm tag-secondary px-4 py-2 rounded-full font-medium hover:scale-105 transition-transform"
                  >
                    {artist}
                  </span>
                ))}
                {event.lineup.length > 6 && (
                  <span className="text-sm text-muted-foreground px-4 py-2 accent-text">
                    +{event.lineup.length - 6} еще
                  </span>
                )}
              </div>
            </div>
          )}

          {event.description && (
            <p className="text-base text-muted-foreground line-clamp-3 leading-relaxed">
              {event.description}
            </p>
          )}

          <div className="flex items-center justify-between pt-6">
            <div className="flex items-center gap-4">
              <span className="text-sm tag-primary px-3 py-1 rounded-full font-medium">
                18+
              </span>
              <span className="text-sm tag-accent px-3 py-1 rounded-full font-medium">
                Electronic
              </span>
            </div>
            
            {event.ticket_url && !isPast && (
              <a
                href={event.ticket_url}
                target="_blank"
                rel="noopener noreferrer"
                className="btn-primary text-base group-hover:scale-105 transition-transform relative z-20"
              >
                Билеты
              </a>
            )}
          </div>
        </div>

        {/* Постер */}
        {event.poster_url && (
          <div className="flex-shrink-0 lg:w-56">
            <div className="relative overflow-hidden rounded-xl subtle-border group-hover:scale-105 transition-transform duration-300">
              <Image
                src={event.poster_url}
                alt={event.title}
                width={224}
                height={300}
                className="w-full lg:w-56 h-72 object-cover"
                onError={(e) => {
                  // Fallback если изображение не загрузилось
                  e.currentTarget.src = `https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=600&fit=crop&auto=format`
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 
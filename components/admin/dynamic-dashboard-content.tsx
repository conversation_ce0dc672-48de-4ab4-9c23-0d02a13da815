'use client'

import dynamic from 'next/dynamic'

// Динамически импортируем DashboardContent с отключением рендеринга на стороне сервера (SSR).
// Также предоставляем компонент-заглушку для состояния загрузки.
export const DynamicDashboardContent = dynamic(
  () => import('./dashboard-content').then((mod) => mod.DashboardContent),
  {
    ssr: false,
    loading: () => (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Загрузка панели...</p>
        </div>
      </div>
    ),
  }
)

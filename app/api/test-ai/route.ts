import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

// Простой тест OpenAI API без аутентификации
export async function POST(request: NextRequest) {
  try {
    const { message } = await request.json()

    if (!message) {
      return NextResponse.json(
        { error: 'Сообщение не указано' },
        { status: 400 }
      )
    }

    // Проверяем переменные окружения
    const apiKey = process.env.OPENAI_API_KEY
    const baseURL = process.env.OPENAI_BASE_URL
    const model = process.env.OPENAI_MODEL

    if (!apiKey || !baseURL || !model) {
      return NextResponse.json({
        error: 'Не настроены переменные окружения',
        config: {
          hasApiKey: !!apiKey,
          hasBaseURL: !!baseURL,
          hasModel: !!model,
          baseURL: baseURL,
          model: model,
        }
      })
    }

    // Инициализируем OpenAI клиент
    const openai = new OpenAI({
      apiKey: apiKey,
      baseURL: baseURL,
    })

    // Простой запрос к AI
    const response = await openai.chat.completions.create({
      model: model,
      messages: [
        {
          role: 'system',
          content: 'Ты помощник для управления мероприятиями. Отвечай на русском языке.',
        },
        {
          role: 'user',
          content: message,
        },
      ],
      temperature: 0.7,
      max_tokens: 500,
    })

    const aiMessage = response.choices[0]?.message?.content

    return NextResponse.json({
      success: true,
      message: aiMessage,
      config: {
        model: model,
        baseURL: baseURL,
      },
      usage: response.usage,
    })
  } catch (error) {
    console.error('Test AI Error:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Неизвестная ошибка',
        details: error,
      },
      { status: 500 }
    )
  }
}

# Руководство по аутентификации

## Обзор

Система аутентификации для админ-панели 16 om on Air использует современную аутентификацию Supabase по email и паролю с поддержкой сброса пароля.

## Возможности

### 1. Регистрация нового аккаунта
- Перейдите на `/admin/login`
- Выберите вкладку "Регистрация"
- Введите email и пароль (минимум 6 символов)
- Подтвердите пароль
- Нажмите "Зарегистрироваться"
- Проверьте почту для подтверждения email

### 2. Вход в систему
- Перейдите на `/admin/login`
- Вкладка "Вход" (по умолчанию)
- Введите email и пароль
- Нажмите "Войти"
- Вы будете перенаправлены в админ-панель

### 3. Сброс пароля
- Перейдите на `/admin/login`
- Выберите вкладку "Сброс"
- Введите ваш email
- Нажмите "Отправить ссылку"
- Проверьте почту и перейдите по ссылке
- Страница автоматически переключится в режим "Новый пароль"
- Введите новый пароль дважды
- Нажмите "Обновить пароль"

## Технические детали

### Обработка сброса пароля
- Supabase отправляет токены аутентификации в URL hash (fragment)
- Токены обрабатываются на клиенте через функцию `handleAuthCallback()`
- После установки сессии, hash очищается из URL
- Пользователь может безопасно установить новый пароль

### Безопасность
- Токены сброса пароля имеют ограниченное время жизни
- Токены одноразовые - после использования становятся недействительными
- Все токены передаются через защищенное HTTPS соединение

## Настройки Supabase

Для корректной работы убедитесь, что в Supabase Dashboard настроено:

### Authentication Settings
- **Enable email confirmations**: ✅ ON
- **Enable email provider**: ✅ ON
- **Confirm email**: ⚠️ OFF (для упрощения)
- **Double confirm email changes**: ⚠️ OFF

### Redirect URLs
Добавьте следующие URL в настройки:
- `http://localhost:3000/admin/login` (для разработки)
- `https://your-domain.com/admin/login` (для продакшена)

**Важно**: Redirect URL должен указывать на `/admin/login`, а не на `/auth/callback`, так как токены обрабатываются на клиенте.

## Обработка ошибок

Система автоматически обрабатывает следующие ошибки:

### Ошибки регистрации
- Пользователь уже существует
- Слишком короткий пароль
- Регистрация отключена

### Ошибки входа
- Неверный email или пароль
- Email не подтвержден
- Слишком много попыток

### Ошибки сброса пароля
- Неверный email
- Превышен лимит запросов
- Недействительная или истекшая ссылка

## Архитектура

### Клиентская обработка
- Функция `handleAuthCallback()` проверяет URL hash на наличие токенов
- При обнаружении токенов устанавливается сессия Supabase
- URL очищается от токенов для безопасности

### Серверная обработка
- Route `/auth/callback` обрабатывает только обычный вход с кодом
- Сброс пароля полностью обрабатывается на клиенте
- Middleware защищает админские маршруты

## Миграция с Magic Links

Если у вас уже есть аккаунт, созданный через Magic Link:
1. Попробуйте войти с вашим email и любым паролем
2. Если не получается, используйте сброс пароля
3. Система автоматически обновит ваш аккаунт

## Поддержка

При возникновении проблем проверьте:
1. Настройки аутентификации в Supabase Dashboard
2. Правильность Redirect URLs (должны указывать на `/admin/login`)
3. Переменные окружения в `.env.local`
4. Логи в браузере (Developer Tools → Console)
5. Логи в Supabase Dashboard → Authentication → Logs

## Отладка

### Проверка токенов в URL
Если сброс пароля не работает, откройте Developer Tools → Console и проверьте:
```javascript
// Проверить наличие токенов в URL hash
console.log(window.location.hash)

// Должно содержать access_token и refresh_token
```

### Проверка сессии
```javascript
// Проверить текущую сессию
import { supabase } from '@/lib/supabase/client'
const { data } = await supabase.auth.getSession()
console.log(data.session)
``` 
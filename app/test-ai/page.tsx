'use client'

import { useState } from 'react'

export default function TestAIPage() {
  const [message, setMessage] = useState('')
  const [response, setResponse] = useState('')
  const [loading, setLoading] = useState(false)

  const testAI = async () => {
    setLoading(true)
    try {
      const res = await fetch('/api/test-ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message }),
      })
      
      const data = await res.json()
      setResponse(JSON.stringify(data, null, 2))
    } catch (error) {
      setResponse(`Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Тестирование AI интеграции</h1>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            Сообщение для AI:
          </label>
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="w-full p-2 border rounded"
            placeholder="Например: Создай мероприятие концерт в Москве"
          />
        </div>
        
        <button
          onClick={testAI}
          disabled={loading || !message}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          {loading ? 'Отправка...' : 'Отправить'}
        </button>
        
        {response && (
          <div>
            <label className="block text-sm font-medium mb-2">
              Ответ AI:
            </label>
            <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
              {response}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}

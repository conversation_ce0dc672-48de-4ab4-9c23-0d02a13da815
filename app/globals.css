@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Светлая тема - с зелеными и розовыми акцентами */
    --background: 0 0% 98%;
    --foreground: 0 0% 8%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 8%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 8%;
    --primary: 155 35% 40%;  /* Темно-зеленый */
    --primary-foreground: 0 0% 100%;
    --secondary: 320 25% 45%; /* Приглушенный розовый */
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --accent: 290 30% 55%; /* Фиолетово-розовый */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 65% 55%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 155 35% 40%;
    --radius: 0.75rem;
  }

  .dark {
    /* Темная тема - андеграундные зеленые и розовые */
    --background: 0 0% 3%;
    --foreground: 0 0% 95%;
    --card: 0 0% 6%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 6%;
    --popover-foreground: 0 0% 95%;
    --primary: 155 40% 60%;  /* Яркий зеленый */
    --primary-foreground: 0 0% 8%;
    --secondary: 320 35% 65%; /* Яркий розовый */
    --secondary-foreground: 0 0% 8%;
    --muted: 0 0% 12%;
    --muted-foreground: 0 0% 65%;
    --accent: 290 40% 70%; /* Яркий фиолетово-розовый */
    --accent-foreground: 0 0% 8%;
    --destructive: 0 55% 50%;
    --destructive-foreground: 0 0% 95%;
    --border: 0 0% 18%;
    --input: 0 0% 18%;
    --ring: 155 40% 60%;
  }

  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    font-variation-settings: normal;
    line-height: 1.6;
    /* Тонкие зеленые и розовые блики */
    background-image: 
      radial-gradient(circle at 25% 75%, hsl(155 40% 60% / 0.03) 0%, transparent 50%),
      radial-gradient(circle at 75% 25%, hsl(320 35% 65% / 0.03) 0%, transparent 50%);
  }
}

@layer components {
  .event-card {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    box-shadow: 
      0 2px 10px -2px hsl(0 0% 0% / 0.05),
      0 4px 20px -4px hsl(0 0% 0% / 0.03);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
  }

  .event-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, 
      hsl(155 40% 60%), 
      hsl(320 35% 65%),
      hsl(290 40% 70%));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .event-card:hover {
    border-color: hsl(var(--primary) / 0.4);
    box-shadow: 
      0 4px 20px -4px hsl(155 40% 60% / 0.15),
      0 8px 40px -8px hsl(320 35% 65% / 0.1);
    transform: translateY(-1px);
  }

  .event-card:hover::before {
    opacity: 1;
  }

  .event-date {
    color: hsl(var(--primary));
    font-weight: 800;
    font-size: 2.2rem;
    line-height: 1.1;
    font-family: 'Space Grotesk', sans-serif;
  }

  @media (min-width: 768px) {
    .event-date {
      font-size: 2.8rem;
    }
  }

  .event-title {
    color: hsl(var(--foreground));
    font-weight: 700;
    font-size: 1.4rem;
    line-height: 1.3;
    font-family: 'Poppins', sans-serif;
  }

  .event-venue {
    color: hsl(var(--muted-foreground));
    font-weight: 500;
    font-size: 0.9rem;
    opacity: 0.8;
  }

  .btn-primary {
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: calc(var(--radius) - 2px);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    box-shadow: 0 2px 8px hsl(var(--primary) / 0.25);
    font-family: 'Poppins', sans-serif;
  }

  .btn-primary:hover {
    background: hsl(var(--primary) / 0.9);
    box-shadow: 0 4px 16px hsl(var(--primary) / 0.35);
    transform: translateY(-1px);
  }

  .btn-secondary {
    background: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: calc(var(--radius) - 2px);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px hsl(var(--secondary) / 0.25);
    font-family: 'Poppins', sans-serif;
  }

  .btn-secondary:hover {
    background: hsl(var(--secondary) / 0.9);
    box-shadow: 0 4px 16px hsl(var(--secondary) / 0.35);
    transform: translateY(-1px);
  }

  .brand-logo {
    color: hsl(var(--foreground));
    font-weight: 800;
    letter-spacing: -0.02em;
    font-family: 'Space Grotesk', sans-serif;
    position: relative;
  }

  .accent-text {
    color: hsl(var(--accent));
    font-weight: 600;
  }

  .tag-primary {
    background: hsl(var(--primary) / 0.1);
    color: hsl(var(--primary));
    border: 1px solid hsl(var(--primary) / 0.2);
  }

  .tag-secondary {
    background: hsl(var(--secondary) / 0.1);
    color: hsl(var(--secondary));
    border: 1px solid hsl(var(--secondary) / 0.2);
  }

  .tag-accent {
    background: hsl(var(--accent) / 0.1);
    color: hsl(var(--accent));
    border: 1px solid hsl(var(--accent) / 0.2);
  }

  /* Admin Panel Styles */
  .admin-header {
    background: hsl(var(--card));
    border-bottom: 1px solid hsl(var(--border));
    padding: 1rem 1.5rem;
    position: sticky;
    top: 0;
    z-index: 40;
    box-shadow: 0 1px 3px hsl(0 0% 0% / 0.03);
  }

  .admin-header-inner {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .admin-header-left,
  .admin-header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
  }

  .admin-panel-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: hsl(var(--accent));
    background: hsl(var(--accent) / 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 999px;
    border: 1px solid hsl(var(--accent) / 0.2);
  }

  .admin-user-email {
    font-size: 0.9rem;
    color: hsl(var(--muted-foreground));
  }

  .admin-signout-btn {
    font-size: 0.9rem;
    font-weight: 600;
    color: hsl(var(--destructive));
    background: transparent;
    border: none;
    cursor: pointer;
    transition: color 0.2s;
  }

  .admin-signout-btn:hover {
    color: hsl(var(--destructive) / 0.8);
  }

  .admin-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1.5rem;
  }

  .admin-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
  }

  .admin-title {
    font-size: 1.8rem;
    font-weight: 700;
    font-family: 'Poppins', sans-serif;
  }

  .admin-empty {
    text-align: center;
    padding: 4rem 1rem;
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
  }

  .admin-empty-text {
    font-size: 1.1rem;
    color: hsl(var(--muted-foreground));
    margin-bottom: 1.5rem;
  }

  .admin-event-list {
    display: grid;
    gap: 1.5rem;
  }

  .admin-event-card {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    transition: all 0.2s ease;
  }

  .admin-event-card:hover {
    border-color: hsl(var(--primary) / 0.5);
    box-shadow: 0 4px 15px hsl(0 0% 0% / 0.05);
  }

  .admin-event-card__main {
    flex: 1;
  }

  .admin-event-card__title {
    font-size: 1.2rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    margin-bottom: 0.75rem;
  }

  .admin-event-card__meta {
    font-size: 0.9rem;
    color: hsl(var(--muted-foreground));
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .admin-event-card__meta strong {
    color: hsl(var(--foreground));
    font-weight: 500;
  }

  .admin-event-card__actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid hsl(var(--border));
    margin-top: 1rem;
  }

  .admin-event-card__action {
    font-size: 0.85rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: calc(var(--radius) - 4px);
    transition: all 0.2s ease;
    text-decoration: none;
    border: 1px solid transparent;
  }

  .admin-event-card__action--view {
    background: hsl(var(--primary) / 0.1);
    color: hsl(var(--primary));
    border-color: hsl(var(--primary) / 0.2);
  }
  .admin-event-card__action--view:hover {
    background: hsl(var(--primary) / 0.2);
  }

  .admin-event-card__action--edit {
    background: hsl(var(--secondary) / 0.1);
    color: hsl(var(--secondary));
    border-color: hsl(var(--secondary) / 0.2);
  }
  .admin-event-card__action--edit:hover {
    background: hsl(var(--secondary) / 0.2);
  }

  .admin-event-card__action--delete {
    background: hsl(var(--destructive) / 0.1);
    color: hsl(var(--destructive));
    border-color: hsl(var(--destructive) / 0.2);
    cursor: pointer;
  }
  .admin-event-card__action--delete:hover {
    background: hsl(var(--destructive) / 0.2);
    color: hsl(var(--destructive));
  }

  @media (min-width: 768px) {
    .admin-event-card {
      flex-direction: row;
      align-items: center;
    }
    .admin-event-card__actions {
      padding-top: 0;
      border-top: none;
      margin-top: 0;
      margin-left: auto;
    }
  }
}

'use client'

import { useState, useEffect } from 'react'
import { createEvent, uploadPoster } from '@/lib/events'
import { getCurrentUser } from '@/lib/auth'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'

export default function NewEventPage() {
  const [user, setUser] = useState<{ email: string } | null>(null)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    title: '',
    event_date: '',
    city: '',
    venue: '',
    description: '',
    ticket_url: '',
    lineup: '',
  })
  const [posterFile, setPosterFile] = useState<File | null>(null)
  const router = useRouter()

  useEffect(() => {
    checkAuth()
  }, []) // eslint-disable-line react-hooks/exhaustive-deps

  const checkAuth = async () => {
    try {
      const currentUser = await getCurrentUser()
      if (!currentUser) {
        router.push('/admin/login')
        return
      }
      setUser({ email: currentUser.email || '' })
    } catch (error) {
      console.error('Auth check error:', error)
      router.push('/admin/login')
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setPosterFile(file)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      let posterUrl = ''
      
      if (posterFile) {
        toast.loading('Загрузка постера...')
        posterUrl = await uploadPoster(posterFile)
        toast.dismiss()
      }

      const lineup = formData.lineup
        .split(',')
        .map(artist => artist.trim())
        .filter(artist => artist.length > 0)

      toast.loading('Создание события...')
      await createEvent({
        title: formData.title,
        event_date: formData.event_date,
        city: formData.city,
        venue: formData.venue,
        description: formData.description || null,
        poster_url: posterUrl || null,
        ticket_url: formData.ticket_url || null,
        lineup: lineup.length > 0 ? lineup : null,
      })

      toast.success('Событие успешно создано!')
      router.push('/admin/dashboard')
    } catch (error) {
      console.error('Error creating event:', error)
      toast.error('Ошибка при создании события')
    } finally {
      setLoading(false)
    }
  }

  return !user ? (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-muted-foreground">Загрузка...</p>
      </div>
    </div>
  ) : (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border admin-header">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/" className="brand-logo">
                16 om on Air
              </Link>
              <span className="text-muted-foreground">Админ-панель</span>
            </div>
            <Link
              href="/admin/dashboard"
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              ← Назад к дашборду
            </Link>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 admin-container">
        <div className="max-w-2xl mx-auto event-form-container">
          <Card>
            <CardHeader>
              <CardTitle className="text-3xl">Создать новое событие</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6 event-form">
                <div className="space-y-2">
                  <Label htmlFor="title">Название события *</Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    required
                    placeholder="16 om on Air: Techno Night"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="event_date">Дата и время *</Label>
                    <Input
                      id="event_date"
                      name="event_date"
                      type="datetime-local"
                      value={formData.event_date}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="city">Город *</Label>
                    <Input
                      id="city"
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      required
                      placeholder="Москва"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="venue">Место проведения *</Label>
                  <Input
                    id="venue"
                    name="venue"
                    value={formData.venue}
                    onChange={handleInputChange}
                    required
                    placeholder="Клуб Arma17"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Описание</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={4}
                    placeholder="Описание события..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lineup">Lineup (через запятую)</Label>
                  <Input
                    id="lineup"
                    name="lineup"
                    value={formData.lineup}
                    onChange={handleInputChange}
                    placeholder="Natasha Wax, Sony Vibe, Guest Artist"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ticket_url">Ссылка на билеты</Label>
                  <Input
                    id="ticket_url"
                    name="ticket_url"
                    type="url"
                    value={formData.ticket_url}
                    onChange={handleInputChange}
                    placeholder="https://tickets.example.com"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="poster">Постер</Label>
                  <Input
                    id="poster"
                    name="poster"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                  />
                </div>

                <div className="flex gap-4">
                  <Button type="submit" disabled={loading}>
                    {loading ? 'Создание...' : 'Создать событие'}
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/admin/dashboard">
                      Отмена
                    </Link>
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
} 
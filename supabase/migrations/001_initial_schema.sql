-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create events table
CREATE TABLE events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    event_date TIMESTAMPTZ NOT NULL,
    city TEXT NOT NULL,
    venue TEXT NOT NULL,
    description TEXT,
    poster_url TEXT,
    ticket_url TEXT,
    lineup TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create storage bucket for posters
INSERT INTO storage.buckets (id, name, public) VALUES ('posters', 'posters', true);

-- Set up Row Level Security (RLS)
ALTER TABLE events ENABLE ROW LEVEL SECURITY;

-- Allow public read access to events
CREATE POLICY "Public read access" ON events
    FOR SELECT USING (true);

-- Allow authenticated users to insert, update, and delete events
CREATE POLICY "Authenticated users can insert" ON events
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update" ON events
    FOR UPDATE USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete" ON events
    FOR DELETE USING (auth.role() = 'authenticated');

-- Storage policies for posters bucket
CREATE POLICY "Public read access to posters" ON storage.objects
    FOR SELECT USING (bucket_id = 'posters');

CREATE POLICY "Authenticated users can upload posters" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'posters' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update posters" ON storage.objects
    FOR UPDATE USING (bucket_id = 'posters' AND auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can delete posters" ON storage.objects
    FOR DELETE USING (bucket_id = 'posters' AND auth.role() = 'authenticated');

-- Create index for better performance on event_date queries
CREATE INDEX idx_events_event_date ON events(event_date);
CREATE INDEX idx_events_city ON events(city); 
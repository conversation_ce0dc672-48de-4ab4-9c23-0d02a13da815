'use client'

import Link from 'next/link'
import { Event } from '@/lib/supabase/client'
import { format } from 'date-fns'
import { ru } from 'date-fns/locale'

interface AdminEventCardProps {
  event: Event
  onDelete: (id: string) => void
}

export function AdminEventCard({ event, onDelete }: AdminEventCardProps) {
  return (
    <div className="admin-event-card">
      <div className="admin-event-card__main">
        <h3 className="admin-event-card__title">{event.title}</h3>
        <div className="admin-event-card__meta">
          <p>
            <strong>Дата:</strong>{' '}
            {format(new Date(event.event_date), 'dd MMMM yyyy, HH:mm', {
              locale: ru,
            })}
          </p>
          <p>
            <strong>Место:</strong> {event.city} • {event.venue}
          </p>
          {event.lineup && event.lineup.length > 0 && (
            <p>
              <strong>Lineup:</strong> {event.lineup.join(', ')}
            </p>
          )}
        </div>
      </div>
      <div className="admin-event-card__actions">
        <Link
          href={`/event/${event.id}`}
          className="admin-event-card__action admin-event-card__action--view"
          target="_blank"
        >
          Просмотр
        </Link>
        <Link
          href={`/admin/event/${event.id}/edit`}
          className="admin-event-card__action admin-event-card__action--edit"
        >
          Редактировать
        </Link>
        <button
          onClick={() => onDelete(event.id)}
          className="admin-event-card__action admin-event-card__action--delete"
        >
          Удалить
        </button>
      </div>
    </div>
  )
}

# 16 om on Air - Афиша событий

Готовая афиша для дуэта Natasha Wax & Sony Vibe с админ-панелью для управления событиями.

## Технологии

- **Frontend**: Next.js 14 (App Router), TypeScript, Tailwind CSS
- **UI**: shadcn/ui компоненты
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Деплой**: Vercel

## Функциональность

### Публичная часть
- 📅 Лента событий (предстоящие и прошедшие)
- 🎵 Детальная страница события с постером
- 📱 Адаптивный дизайн (mobile-first)
- ⚡ Real-time обновления без перезагрузки
- 🎨 Фирменный дизайн с брендингом 16 om

### Админ-панель
- 🔐 Полноценная аутентификация (регистрация, вход, сброс пароля)
- 📧 Подтверждение email при регистрации
- 🔒 Безопасный вход по email и паролю
- ➕ Создание/редактирование/удаление событий
- 🖼️ Загрузка постеров в Supabase Storage
- 📊 Дашборд со списком всех событий
- 🔄 Мгновенные обновления на сайте

## Быстрый старт

1. **Установить зависимости**
```bash
npm install
```

2. **Настроить базу данных**
```bash
npm run setup-database
```

3. **Запустить локально**
```bash
npm run dev
```

4. **Доступ к админ-панели**
- Перейти на `/admin/login`
- Зарегистрироваться или войти с email и паролем
- Подтвердить email при регистрации (проверить почту)
- Создавать события в админ-панели

## Реальные данные

Приложение уже содержит реальные события дуэта 16 om on Air:

### Предстоящие события (2025)
- **15 июля** - Melodic Techno Night в Gipsy (Москва)
- **22 июля** - Summer Vibes в Stackenschneider (СПб)
- **5 августа** - Rooftop Session в Roof Bar Sixty (Москва)
- **12 августа** - Deep Underground в Warehouse Club (Екатеринбург)
- **20 августа** - Electronic Waves Festival (Казань)
- **2 сентября** - Intimate Club Set в Gaudi Hall (Новосибирск)

### Прошедшие события (2024)
- **31 декабря** - New Year Special в Icon Club (Москва)
- **15 декабря** - Winter Warmth в Griboedov (СПб)
- **20 ноября** - Anniversary Show в Mutabor (Москва)

## Особенности

- ✅ **Готов к продакшену** - полностью настроенная база данных
- ✅ **Реальные данные** - события с афишами и описаниями
- ✅ **Адаптивный дизайн** - отлично работает на всех устройствах
- ✅ **Real-time обновления** - изменения видны мгновенно
- ✅ **SEO оптимизация** - мета-теги и Open Graph
- ✅ **Безопасность** - Row Level Security в Supabase

## Деплой на Vercel

1. **Подключить репозиторий к Vercel**
2. **Добавить переменные окружения**:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
3. **Деплой** - автоматически при push в main

## Управление событиями

### Создание события
1. Войти в админ-панель (`/admin/login`)
2. Перейти в дашборд (`/admin/dashboard`)
3. Нажать "Создать событие"
4. Заполнить форму и загрузить постер
5. Сохранить - событие появится на сайте мгновенно

### Редактирование
- Все события можно редактировать через админ-панель
- Изменения применяются в real-time
- Поддержка загрузки и замены постеров

## Структура проекта

```
afisha/
├── app/                    # Next.js App Router
│   ├── page.tsx           # Главная страница
│   ├── event/[id]/        # Страница события
│   └── admin/             # Админ-панель
├── components/            # React компоненты
├── lib/                   # Утилиты и API
├── scripts/               # Скрипты настройки
└── supabase/             # Миграции БД
```

## API

### События
- `getAllEvents()` - все события
- `getUpcomingEvents()` - только предстоящие
- `getEventById(id)` - событие по ID
- `createEvent(data)` - создание
- `updateEvent(id, data)` - обновление
- `deleteEvent(id)` - удаление

### Аутентификация
- `signInWithEmail(email)` - magic link
- `signOut()` - выход
- `getUser()` - текущий пользователь

## Поддержка

Приложение полностью готово к использованию и содержит все необходимые данные для демонстрации функциональности.

'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Upload, Send, Bot, User, Image as ImageIcon, Check, X, Edit } from 'lucide-react'
import { toast } from 'sonner'

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  image?: string
  event_preview?: any
  requires_confirmation?: boolean
}

interface EventPreview {
  title: string
  event_date: string
  city: string
  venue: string
  description?: string
  lineup?: string[]
  ticket_url?: string
  confidence: number
  extracted_from: 'text' | 'image'
}

export function AIChat() {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'Привет! Я AI-ассистент для управления мероприятиями. Могу помочь создать, редактировать или удалить мероприятия. Также могу обработать афиши - просто загрузите изображение!',
      timestamp: new Date(),
    },
  ])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [pendingConfirmation, setPendingConfirmation] = useState<any>(null)
  const [posterUrl, setPosterUrl] = useState<string | null>(null)
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (!file.type.startsWith('image/')) {
        toast.error('Поддерживаются только изображения')
        return
      }
      
      if (file.size > 10 * 1024 * 1024) {
        toast.error('Размер файла не должен превышать 10MB')
        return
      }

      setSelectedImage(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const clearImage = () => {
    setSelectedImage(null)
    setImagePreview(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const sendMessage = async () => {
    if (!inputValue.trim() && !selectedImage) return

    setIsLoading(true)
    
    try {
      let imageBase64: string | undefined
      let posterUrl: string | undefined

      // Если есть изображение, сначала загружаем его
      if (selectedImage) {
        const formData = new FormData()
        formData.append('file', selectedImage)

        const uploadResponse = await fetch('/api/ai/upload', {
          method: 'POST',
          body: formData,
        })

        if (!uploadResponse.ok) {
          throw new Error('Ошибка при загрузке изображения')
        }

        const uploadResult = await uploadResponse.json()
        imageBase64 = uploadResult.base64
        posterUrl = uploadResult.poster_url
        setPosterUrl(uploadResult.poster_url)
      }

      // Добавляем сообщение пользователя
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'user',
        content: inputValue || 'Загружено изображение',
        timestamp: new Date(),
        image: imagePreview || undefined,
      }

      setMessages(prev => [...prev, userMessage])

      // Отправляем запрос к AI
      const response = await fetch('/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messages: [...messages, userMessage].map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          imageBase64,
          posterUrl,
        }),
      })

      if (!response.ok) {
        throw new Error('Ошибка при обращении к AI')
      }

      const aiResponse = await response.json()

      // Добавляем ответ AI
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: aiResponse.message,
        timestamp: new Date(),
        event_preview: aiResponse.event_preview,
        requires_confirmation: aiResponse.requires_confirmation,
      }

      setMessages(prev => [...prev, assistantMessage])

      // Если требуется подтверждение, сохраняем данные
      if (aiResponse.requires_confirmation && aiResponse.event_preview) {
        setPendingConfirmation(aiResponse.event_preview)
      }

      // Очищаем форму
      setInputValue('')
      clearImage()
    } catch (error) {
      console.error('Chat error:', error)
      toast.error('Произошла ошибка при отправке сообщения')
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: 'Извините, произошла ошибка. Попробуйте еще раз.',
        timestamp: new Date(),
      }
      
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  const handleConfirmation = async (confirmed: boolean) => {
    if (!pendingConfirmation) return

    setIsLoading(true)

    try {
      if (confirmed) {
        // Отправляем подтверждение создания
        const response = await fetch('/api/ai/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            messages: [
              ...messages,
              {
                role: 'user',
                content: 'Подтверждаю создание мероприятия',
              },
            ],
            confirmed_event: pendingConfirmation,
            posterUrl: posterUrl,
          }),
        })

        if (!response.ok) {
          throw new Error('Ошибка при создании мероприятия')
        }

        const result = await response.json()
        
        const confirmMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: result.message || 'Мероприятие успешно создано!',
          timestamp: new Date(),
        }

        setMessages(prev => [...prev, confirmMessage])
        toast.success('Мероприятие создано!')
      } else {
        const cancelMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: 'Создание мероприятия отменено. Если хотите внести изменения, опишите что нужно исправить.',
          timestamp: new Date(),
        }

        setMessages(prev => [...prev, cancelMessage])
      }
    } catch (error) {
      console.error('Confirmation error:', error)
      toast.error('Ошибка при обработке подтверждения')
    } finally {
      setPendingConfirmation(null)
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <Card className="h-[600px] flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bot className="h-5 w-5" />
          AI Ассистент
        </CardTitle>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col gap-4">
        {/* Область сообщений */}
        <div className="flex-1 overflow-y-auto space-y-4 pr-2">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${
                message.role === 'user' ? 'justify-end' : 'justify-start'
              }`}
            >
              <div
                className={`flex gap-3 max-w-[80%] ${
                  message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                }`}
              >
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                    message.role === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {message.role === 'user' ? (
                    <User className="h-4 w-4" />
                  ) : (
                    <Bot className="h-4 w-4" />
                  )}
                </div>
                
                <div className="space-y-2">
                  <div
                    className={`rounded-lg p-3 ${
                      message.role === 'user'
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}
                  >
                    {message.image && (
                      <div className="mb-2">
                        <img
                          src={message.image}
                          alt="Загруженное изображение"
                          className="max-w-full h-auto rounded"
                        />
                      </div>
                    )}
                    <div className="whitespace-pre-wrap">{message.content}</div>
                  </div>
                  
                  {/* Превью мероприятия */}
                  {message.event_preview && (
                    <div className="bg-white border rounded-lg p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="font-semibold text-lg">{message.event_preview.title}</h4>
                        <Badge variant="secondary">
                          {Math.round(message.event_preview.confidence * 100)}%
                        </Badge>
                      </div>
                      
                      <div className="space-y-2 text-sm">
                        <div><strong>Дата:</strong> {formatDate(message.event_preview.event_date)}</div>
                        <div><strong>Место:</strong> {message.event_preview.city}, {message.event_preview.venue}</div>
                        {message.event_preview.description && (
                          <div><strong>Описание:</strong> {message.event_preview.description}</div>
                        )}
                        {message.event_preview.lineup?.length > 0 && (
                          <div><strong>Lineup:</strong> {message.event_preview.lineup.join(', ')}</div>
                        )}
                        {message.event_preview.ticket_url && (
                          <div><strong>Билеты:</strong> {message.event_preview.ticket_url}</div>
                        )}
                      </div>
                      
                      {message.requires_confirmation && pendingConfirmation && (
                        <div className="flex gap-2 pt-2">
                          <Button
                            size="sm"
                            onClick={() => handleConfirmation(true)}
                            disabled={isLoading}
                            className="flex items-center gap-1"
                          >
                            <Check className="h-4 w-4" />
                            Подтвердить
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleConfirmation(false)}
                            disabled={isLoading}
                            className="flex items-center gap-1"
                          >
                            <X className="h-4 w-4" />
                            Отменить
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="flex items-center gap-1"
                          >
                            <Edit className="h-4 w-4" />
                            Редактировать
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                  
                  <div className="text-xs text-gray-500">
                    {message.timestamp.toLocaleTimeString('ru-RU', {
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </div>
                </div>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>

        <Separator />

        {/* Превью изображения */}
        {imagePreview && (
          <div className="relative">
            <img
              src={imagePreview}
              alt="Превью"
              className="max-h-32 rounded border"
            />
            <Button
              size="sm"
              variant="destructive"
              className="absolute top-1 right-1"
              onClick={clearImage}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Форма ввода */}
        <div className="flex gap-2">
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageSelect}
            className="hidden"
          />
          
          <Button
            variant="outline"
            size="icon"
            onClick={() => fileInputRef.current?.click()}
            disabled={isLoading}
          >
            <Upload className="h-4 w-4" />
          </Button>
          
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Напишите сообщение или загрузите афишу..."
            disabled={isLoading}
            className="flex-1"
          />
          
          <Button
            onClick={sendMessage}
            disabled={isLoading || (!inputValue.trim() && !selectedImage)}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

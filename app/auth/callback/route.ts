import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const error = requestUrl.searchParams.get('error')
  const error_description = requestUrl.searchParams.get('error_description')

  // Если есть ошибка, перенаправляем на страницу входа с ошибкой
  if (error) {
    console.error('Auth callback error:', error, error_description)
    return NextResponse.redirect(
      new URL(`/admin/login?error=callback_error&details=${encodeURIComponent(error_description || error)}`, requestUrl.origin)
    )
  }

  // Если есть код, обрабатываем его для обычного входа
  if (code) {
    try {
      const cookieStore = cookies()
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
      const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code)

      if (exchangeError) {
        console.error('Code exchange error:', exchangeError)
        return NextResponse.redirect(
          new URL('/admin/login?error=auth_failed', requestUrl.origin)
        )
      }

      // Проверяем, что пользователь действительно аутентифицирован
      if (!data.user) {
        console.error('No user data after code exchange')
        return NextResponse.redirect(
          new URL('/admin/login?error=no_auth_data', requestUrl.origin)
        )
      }

      // Обычный вход - перенаправляем в админ-панель
      return NextResponse.redirect(new URL('/admin/dashboard', requestUrl.origin))

    } catch (error) {
      console.error('Callback processing error:', error)
      return NextResponse.redirect(
        new URL('/admin/login?error=callback_error', requestUrl.origin)
      )
    }
  }

  // Если нет кода, перенаправляем на страницу входа
  // Это может быть сброс пароля, который будет обработан на клиенте
  return NextResponse.redirect(
    new URL('/admin/login', requestUrl.origin)
  )
} 